import React from "react";
import { createStackNavigator } from "@react-navigation/stack";
import { useAuthStore } from "../stores/authStore";
import WelcomeScreen from "../screens/onboarding/WelcomeScreen";
import LoginScreen from "../screens/onboarding/LoginScreen";
import SignupScreen from "../screens/onboarding/SignupScreen";
import ForgotPasswordScreen from "../screens/onboarding/ForgotPasswordScreen";
import FarmerDetailsScreen from "../screens/onboarding/FarmerDetailsScreen";
import VerificationScreen from "../screens/onboarding/VerificationScreen";
export type OnboardingStackParamList = {
  Welcome: undefined;
  Login: undefined;
  Signup: undefined;
  ForgotPassword: undefined;
  FarmerDetails: undefined;
  Verification: { email?: string };
};

const Stack = createStackNavigator<OnboardingStackParamList>();

export default function OnboardingNavigator() {
  const { isAuthenticated, user } = useAuthStore();

  // If user is authenticated and needs to complete farmer onboarding, show FarmerDetails directly
  if (
    isAuthenticated &&
    user &&
    !user.onboardingComplete &&
    user.role === "farmer"
  ) {
    return (
      <Stack.Navigator screenOptions={{ headerShown: false }}>
        <Stack.Screen name="FarmerDetails" component={FarmerDetailsScreen} />
        <Stack.Screen name="Welcome" component={WelcomeScreen} />
        <Stack.Screen name="Login" component={LoginScreen} />
        <Stack.Screen name="Signup" component={SignupScreen} />
        <Stack.Screen name="ForgotPassword" component={ForgotPasswordScreen} />
        <Stack.Screen name="Verification" component={VerificationScreen} />
      </Stack.Navigator>
    );
  }

  // If user is authenticated but needs email verification, show Verification
  if (isAuthenticated && user && !user.isEmailVerified) {
    return (
      <Stack.Navigator screenOptions={{ headerShown: false }}>
        <Stack.Screen name="Verification" component={VerificationScreen} />
        <Stack.Screen name="Welcome" component={WelcomeScreen} />
        <Stack.Screen name="Login" component={LoginScreen} />
        <Stack.Screen name="Signup" component={SignupScreen} />
        <Stack.Screen name="ForgotPassword" component={ForgotPasswordScreen} />
        <Stack.Screen name="FarmerDetails" component={FarmerDetailsScreen} />
      </Stack.Navigator>
    );
  }

  // Default navigator for non-authenticated users
  return (
    <Stack.Navigator screenOptions={{ headerShown: false }}>
      <Stack.Screen name="Welcome" component={WelcomeScreen} />
      <Stack.Screen name="Login" component={LoginScreen} />
      <Stack.Screen name="Signup" component={SignupScreen} />
      <Stack.Screen name="ForgotPassword" component={ForgotPasswordScreen} />
      <Stack.Screen name="FarmerDetails" component={FarmerDetailsScreen} />
      <Stack.Screen name="Verification" component={VerificationScreen} />
    </Stack.Navigator>
  );
}
