import React from "react";
import { createBottomTabNavigator } from "@react-navigation/bottom-tabs";
import { Ionicons } from "@expo/vector-icons";
import { getFocusedRouteNameFromRoute } from "@react-navigation/native";
import DashboardNavigator from "./DashboardNavigator";
import PlannerNavigator from "./PlannerNavigator";
import MarketplaceNavigator from "./MarketplaceNavigator";
import ReportsNavigator from "./ReportsNavigator";
import ProfileNavigator from "./ProfileNavigator";

export type MainTabParamList = {
  Dashboard: undefined;
  Planner: undefined;
  Marketplace:
    | {
        screen: string;
        params?: { initialTab?: "input" | "output" };
      }
    | undefined;
  Reports: undefined;
  Profile: undefined;
};

const Tab = createBottomTabNavigator<MainTabParamList>();

// Function to determine if tab bar should be hidden
function getTabBarVisibility(route: any) {
  const routeName = getFocusedRouteNameFromRoute(route);

  // Hide tab bar on these screens
  const hideTabBarScreens = [
    "AllProducts",
    "ProductDetail",
    "Cart",
    "Checkout",
    "Payment",
    "OrderConfirmation",
    "OrderHistory",
    "OrderDetail",
    "EditProfile",
    "EditFarmerDetails",
    "DeclareHarvest",
  ];

  return hideTabBarScreens.includes(routeName || "") ? "none" : "flex";
}

export default function MainNavigator() {
  return (
    <Tab.Navigator
      screenOptions={({ route }) => ({
        headerShown: false,
        tabBarIcon: ({ focused, color, size }) => {
          let iconName: keyof typeof Ionicons.glyphMap;

          if (route.name === "Dashboard") {
            iconName = focused ? "home" : "home-outline";
          } else if (route.name === "Planner") {
            iconName = focused ? "calendar" : "calendar-outline";
          } else if (route.name === "Marketplace") {
            iconName = focused ? "storefront" : "storefront-outline";
          } else if (route.name === "Reports") {
            iconName = focused ? "bar-chart" : "bar-chart-outline";
          } else if (route.name === "Profile") {
            iconName = focused ? "person" : "person-outline";
          } else {
            iconName = "help-outline";
          }

          return <Ionicons name={iconName} size={size} color={color} />;
        },
        tabBarActiveTintColor: "#16a34a", // green-600
        tabBarInactiveTintColor: "#6b7280", // gray-500
        tabBarHideOnKeyboard: true,
        tabBarVisibilityAnimationConfig: {
          show: { animation: "timing", config: { duration: 0 } },
          hide: { animation: "timing", config: { duration: 0 } },
        },
        tabBarStyle: {
          backgroundColor: "white",
          borderTopColor: "#e5e7eb", // gray-200
          borderTopWidth: 1,
          paddingBottom: 34, // Extra padding for iPhone home indicator
          height: 82, // Reduced height
          paddingHorizontal: 0,
          marginTop: 0,
          elevation: 0,
          shadowOpacity: 0,
          position: "absolute",
          bottom: 0,
          left: 0,
          right: 0,
          borderTopLeftRadius: 0,
          borderTopRightRadius: 0,
          paddingTop: 2,
          display: getTabBarVisibility(route) as any,
        },
        tabBarLabelStyle: {
          fontSize: 10, // Reduced font size
          fontWeight: "500",
          marginTop: 2,
          marginBottom: 0,
        },
        tabBarIconStyle: {
          marginBottom: 2,
        },
      })}
    >
      <Tab.Screen
        name="Dashboard"
        component={DashboardNavigator}
        options={{ tabBarLabel: "Home" }}
      />
      <Tab.Screen
        name="Planner"
        component={PlannerNavigator}
        options={{ tabBarLabel: "Planner" }}
      />
      <Tab.Screen
        name="Marketplace"
        component={MarketplaceNavigator}
        options={{ tabBarLabel: "Marketplace" }}
      />
      <Tab.Screen
        name="Reports"
        component={ReportsNavigator}
        options={{ tabBarLabel: "Reports" }}
      />
      <Tab.Screen
        name="Profile"
        component={ProfileNavigator}
        options={{ tabBarLabel: "Profile" }}
      />
    </Tab.Navigator>
  );
}
