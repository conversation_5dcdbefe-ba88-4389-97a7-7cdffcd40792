import React from "react";
import { createStackNavigator } from "@react-navigation/stack";
import { useAuthStore } from "../stores/authStore";
import OnboardingNavigator from "./OnboardingNavigator";
import MainNavigator from "./MainNavigator";

export type RootStackParamList = {
  Onboarding: undefined;
  Main: undefined;
};

const Stack = createStackNavigator<RootStackParamList>();

export default function RootNavigator() {
  const { isAuthenticated, user } = useAuthStore();

  // Determine if user should see onboarding or main app
  const shouldShowOnboarding = !isAuthenticated || !user?.onboardingComplete;

  return (
    <Stack.Navigator screenOptions={{ headerShown: false }}>
      {shouldShowOnboarding ? (
        <Stack.Screen name="Onboarding" component={OnboardingNavigator} />
      ) : (
        <Stack.Screen name="Main" component={MainNavigator} />
      )}
    </Stack.Navigator>
  );
}
