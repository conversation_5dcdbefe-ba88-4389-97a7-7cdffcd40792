import AsyncStorage from "@react-native-async-storage/async-storage";
import { httpClient, ApiResponse } from "./httpClient";
import { API_CONFIG, STORAGE_KEYS } from "../config/api";

// Request types
export interface SignupRequest {
  firstName: string;
  lastName: string;
  email: string;
  password: string;
  role: "farmer" | "buyer";
}

export interface LoginRequest {
  email: string;
  password: string;
}

export interface VerifyEmailRequest {
  email: string;
  code: string;
}

export interface ResendVerificationRequest {
  email: string;
}

export interface ForgotPasswordRequest {
  email: string;
}

export interface ResetPasswordRequest {
  email: string;
  code: string;
  password: string;
}

export interface ChangePasswordRequest {
  currentPassword: string;
  newPassword: string;
}

// Response types
export interface AuthUser {
  id: string;
  firstName: string;
  lastName: string;
  email: string;
  role: "farmer" | "buyer";
  isEmailVerified: boolean;
  onboardingComplete: boolean;
  lastLogin?: string;
  createdAt?: string;
  updatedAt?: string;
}

export interface AuthResponse {
  user: AuthUser;
  accessToken: string;
  refreshToken: string;
}

export interface ProfileResponse {
  user: AuthUser;
}

class AuthService {
  // Store user data in AsyncStorage
  async storeUserData(user: AuthUser): Promise<void> {
    try {
      await AsyncStorage.setItem(STORAGE_KEYS.USER_DATA, JSON.stringify(user));
    } catch (error) {
      console.error("Error storing user data:", error);
    }
  }

  // Get stored user data
  async getStoredUserData(): Promise<AuthUser | null> {
    try {
      const userData = await AsyncStorage.getItem(STORAGE_KEYS.USER_DATA);
      return userData ? JSON.parse(userData) : null;
    } catch (error) {
      console.error("Error getting stored user data:", error);
      return null;
    }
  }

  // Clear all stored data
  async clearStoredData(): Promise<void> {
    try {
      await httpClient.clearTokens();
      await AsyncStorage.removeItem(STORAGE_KEYS.USER_DATA);
    } catch (error) {
      console.error("Error clearing stored data:", error);
    }
  }

  // Check if user is authenticated
  async isAuthenticated(): Promise<boolean> {
    try {
      const accessToken = await AsyncStorage.getItem(STORAGE_KEYS.ACCESS_TOKEN);
      const userData = await this.getStoredUserData();
      return !!(accessToken && userData);
    } catch (error) {
      console.error("Error checking authentication:", error);
      return false;
    }
  }

  // Sign up new user
  async signup(data: SignupRequest): Promise<ApiResponse<AuthResponse>> {
    const response = await httpClient.post<AuthResponse>(
      API_CONFIG.ENDPOINTS.SIGNUP,
      data
    );

    if (response.success && response.data) {
      const { user, accessToken, refreshToken } = response.data;
      await httpClient.storeTokens(accessToken, refreshToken);
      await this.storeUserData(user);
    }

    return response;
  }

  // Login user
  async login(data: LoginRequest): Promise<ApiResponse<AuthResponse>> {
    const response = await httpClient.post<AuthResponse>(
      API_CONFIG.ENDPOINTS.LOGIN,
      data
    );

    if (response.success && response.data) {
      const { user, accessToken, refreshToken } = response.data;
      await httpClient.storeTokens(accessToken, refreshToken);
      await this.storeUserData(user);
    }

    return response;
  }

  // Verify email
  async verifyEmail(data: VerifyEmailRequest): Promise<ApiResponse<any>> {
    const response = await httpClient.post(
      API_CONFIG.ENDPOINTS.VERIFY_EMAIL,
      data
    );

    // Update stored user data if verification successful
    if (response.success) {
      const userData = await this.getStoredUserData();
      if (userData) {
        userData.isEmailVerified = true;
        // Update onboardingComplete if provided in response
        if (
          response.data &&
          (response.data as any).user?.onboardingComplete !== undefined
        ) {
          userData.onboardingComplete = (
            response.data as any
          ).user.onboardingComplete;
        }
        await this.storeUserData(userData);
      }
    }

    return response;
  }

  // Resend verification email
  async resendVerification(
    data: ResendVerificationRequest
  ): Promise<ApiResponse<any>> {
    return httpClient.post(API_CONFIG.ENDPOINTS.RESEND_VERIFICATION, data);
  }

  // Forgot password
  async forgotPassword(data: ForgotPasswordRequest): Promise<ApiResponse<any>> {
    return httpClient.post(API_CONFIG.ENDPOINTS.FORGOT_PASSWORD, data);
  }

  // Reset password
  async resetPassword(data: ResetPasswordRequest): Promise<ApiResponse<any>> {
    return httpClient.post(API_CONFIG.ENDPOINTS.RESET_PASSWORD, data);
  }

  // Change password (authenticated)
  async changePassword(data: ChangePasswordRequest): Promise<ApiResponse<any>> {
    return httpClient.authPost(API_CONFIG.ENDPOINTS.CHANGE_PASSWORD, data);
  }

  // Update user profile (authenticated)
  async updateProfile(data: {
    firstName: string;
    lastName: string;
  }): Promise<ApiResponse<ProfileResponse>> {
    const response = await httpClient.authPut<ProfileResponse>(
      API_CONFIG.ENDPOINTS.UPDATE_PROFILE,
      data
    );

    if (response.success && response.data) {
      await this.storeUserData(response.data.user);
    }

    return response;
  }

  // Get user profile (authenticated)
  async getProfile(): Promise<ApiResponse<ProfileResponse>> {
    const response = await httpClient.authGet<ProfileResponse>(
      API_CONFIG.ENDPOINTS.PROFILE
    );

    if (response.success && response.data) {
      await this.storeUserData(response.data.user);
    }

    return response;
  }

  // Logout user
  async logout(): Promise<ApiResponse<any>> {
    try {
      // Call logout endpoint to invalidate tokens on server
      await httpClient.authPost(API_CONFIG.ENDPOINTS.LOGOUT);
    } catch (error) {
      // Continue with local logout even if server call fails
      console.error("Error calling logout endpoint:", error);
    } finally {
      // Always clear local data
      await this.clearStoredData();
    }

    return {
      success: true,
      message: "Logged out successfully",
    };
  }

  // Refresh tokens
  async refreshTokens(): Promise<boolean> {
    try {
      const refreshToken = await AsyncStorage.getItem(
        STORAGE_KEYS.REFRESH_TOKEN
      );
      if (!refreshToken) {
        return false;
      }

      const response = await httpClient.post<{
        accessToken: string;
        refreshToken: string;
      }>(API_CONFIG.ENDPOINTS.REFRESH_TOKEN, { refreshToken });

      if (response.success && response.data) {
        const { accessToken, refreshToken: newRefreshToken } = response.data;
        await httpClient.storeTokens(accessToken, newRefreshToken);
        return true;
      }

      return false;
    } catch (error) {
      console.error("Error refreshing tokens:", error);
      await this.clearStoredData();
      return false;
    }
  }
}

// Export singleton instance
export const authService = new AuthService();
