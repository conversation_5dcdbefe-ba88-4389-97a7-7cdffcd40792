import { create } from "zustand";
import {
  Crop,
  CropPlan,
  FarmingActivity,
  CropRecommendation,
  CreateCropPlanRequest,
  CreateFarmingActivityRequest,
} from "../types";
import CropService from "../services/cropService";

// Type for active crops displayed on dashboard
interface ActiveCrop {
  id: string;
  name: string;
  stage: string;
  progress: number;
  image: string;
}

interface CropStore {
  // Crops
  crops: Crop[];
  selectedCrop: Crop | null;

  // Crop Plans
  cropPlans: CropPlan[];
  selectedCropPlan: CropPlan | null;

  // Computed properties
  activeCrops: ActiveCrop[]; // Active crop plans formatted for dashboard display

  // Activities
  activities: FarmingActivity[];
  calendarActivities: { [key: string]: FarmingActivity[] };
  upcomingActivities: FarmingActivity[];

  // Recommendations
  recommendations: CropRecommendation[];

  // Loading states
  isLoading: boolean;
  isLoadingPlans: boolean;
  isLoadingActivities: boolean;
  isLoadingRecommendations: boolean;

  // Error states
  error: string | null;

  // Actions
  fetchCrops: (params?: any) => Promise<void>;
  fetchCropById: (id: string) => Promise<void>;
  fetchRecommendations: (params?: any) => Promise<void>;

  createCropPlan: (data: CreateCropPlanRequest) => Promise<void>;
  fetchCropPlans: (params?: any) => Promise<void>;
  updateCropPlan: (id: string, data: any) => Promise<void>;
  deleteCropPlan: (id: string) => Promise<void>;

  fetchActivities: (params?: any) => Promise<void>;
  fetchCalendarView: (month?: number, year?: number) => Promise<void>;
  fetchUpcomingActivities: (days?: number) => Promise<void>;
  createActivity: (data: CreateFarmingActivityRequest) => Promise<void>;
  completeActivity: (id: string, data?: any) => Promise<void>;

  setLoading: (loading: boolean) => void;
  setError: (error: string | null) => void;
  clearError: () => void;
  updateActiveCrops: () => void;
}

export const useCropStore = create<CropStore>((set, get) => ({
  // Initial state
  crops: [],
  selectedCrop: null,
  cropPlans: [],
  selectedCropPlan: null,
  activities: [],
  calendarActivities: {},
  upcomingActivities: [],
  recommendations: [],
  isLoading: false,
  isLoadingPlans: false,
  isLoadingActivities: false,
  isLoadingRecommendations: false,
  error: null,

  // Mock active crops data - this will be replaced with real data later
  activeCrops: [
    {
      id: "1",
      name: "Tomatoes",
      stage: "Flowering",
      progress: 75,
      image:
        "https://images.unsplash.com/photo-1592924357228-91a4daadcfea?w=100&h=100&fit=crop&crop=center",
    },
    {
      id: "2",
      name: "Maize",
      stage: "Growing",
      progress: 45,
      image:
        "https://images.unsplash.com/photo-1551754655-cd27e38d2076?w=100&h=100&fit=crop&crop=center",
    },
  ],

  // Actions
  fetchCrops: async (params) => {
    set({ isLoading: true, error: null });
    try {
      const response = await CropService.getCrops(params);
      if (response.success && response.data) {
        set({ crops: response.data.crops });
      } else {
        set({ error: response.message || "Failed to fetch crops" });
      }
    } catch (error: any) {
      set({ error: error.message || "Failed to fetch crops" });
    } finally {
      set({ isLoading: false });
    }
  },

  fetchCropById: async (id) => {
    set({ isLoading: true, error: null });
    try {
      const response = await CropService.getCropById(id);
      if (response.success && response.data) {
        set({ selectedCrop: response.data });
      } else {
        set({ error: response.message || "Failed to fetch crop" });
      }
    } catch (error: any) {
      set({ error: error.message || "Failed to fetch crop" });
    } finally {
      set({ isLoading: false });
    }
  },

  fetchRecommendations: async (params) => {
    set({ isLoadingRecommendations: true, error: null });
    try {
      console.log("Fetching recommendations with params:", params);
      const response = await CropService.getCropRecommendations(params);
      console.log("Recommendations response:", response);

      if (response.success && response.data) {
        console.log("Setting recommendations:", response.data.length, "items");
        set({ recommendations: response.data });
      } else {
        console.log("Recommendations fetch failed:", response.message);
        set({ error: response.message || "Failed to fetch recommendations" });
      }
    } catch (error: any) {
      console.error("Recommendations fetch error:", error);
      set({ error: error.message || "Failed to fetch recommendations" });
    } finally {
      set({ isLoadingRecommendations: false });
    }
  },

  createCropPlan: async (data) => {
    set({ isLoadingPlans: true, error: null });
    try {
      const response = await CropService.createCropPlan(data);
      if (response.success && response.data) {
        const { cropPlans } = get();
        set({ cropPlans: [...cropPlans, response.data] });
      } else {
        set({ error: response.message || "Failed to create crop plan" });
      }
    } catch (error: any) {
      set({ error: error.message || "Failed to create crop plan" });
    } finally {
      set({ isLoadingPlans: false });
    }
  },

  fetchCropPlans: async (params) => {
    set({ isLoadingPlans: true, error: null });
    try {
      const response = await CropService.getCropPlans(params);
      if (response.success && response.data) {
        set({ cropPlans: response.data.cropPlans });
        // Update active crops when crop plans are fetched
        get().updateActiveCrops();
      } else {
        set({ error: response.message || "Failed to fetch crop plans" });
      }
    } catch (error: any) {
      set({ error: error.message || "Failed to fetch crop plans" });
    } finally {
      set({ isLoadingPlans: false });
    }
  },

  updateCropPlan: async (id, data) => {
    set({ isLoadingPlans: true, error: null });
    try {
      const response = await CropService.updateCropPlan(id, data);
      if (response.success && response.data) {
        const { cropPlans } = get();
        set({
          cropPlans: cropPlans.map((plan) =>
            plan._id === id ? response.data! : plan
          ),
        });
      } else {
        set({ error: response.message || "Failed to update crop plan" });
      }
    } catch (error: any) {
      set({ error: error.message || "Failed to update crop plan" });
    } finally {
      set({ isLoadingPlans: false });
    }
  },

  deleteCropPlan: async (id) => {
    set({ isLoadingPlans: true, error: null });
    try {
      const response = await CropService.deleteCropPlan(id);
      if (response.success) {
        const { cropPlans } = get();
        set({ cropPlans: cropPlans.filter((plan) => plan._id !== id) });
      } else {
        set({ error: response.message || "Failed to delete crop plan" });
      }
    } catch (error: any) {
      set({ error: error.message || "Failed to delete crop plan" });
    } finally {
      set({ isLoadingPlans: false });
    }
  },

  fetchActivities: async (params) => {
    set({ isLoadingActivities: true, error: null });
    try {
      const response = await CropService.getFarmingActivities(params);
      if (response.success && response.data) {
        set({ activities: response.data.activities });
      } else {
        set({ error: response.message || "Failed to fetch activities" });
      }
    } catch (error: any) {
      set({ error: error.message || "Failed to fetch activities" });
    } finally {
      set({ isLoadingActivities: false });
    }
  },

  fetchCalendarView: async (month, year) => {
    set({ isLoadingActivities: true, error: null });
    try {
      const response = await CropService.getCalendarView({ month, year });
      if (response.success && response.data) {
        set({ calendarActivities: response.data.activities });
      } else {
        set({ error: response.message || "Failed to fetch calendar view" });
      }
    } catch (error: any) {
      set({ error: error.message || "Failed to fetch calendar view" });
    } finally {
      set({ isLoadingActivities: false });
    }
  },

  fetchUpcomingActivities: async (days) => {
    set({ isLoadingActivities: true, error: null });
    try {
      const response = await CropService.getUpcomingActivities(days);
      if (response.success && response.data) {
        set({ upcomingActivities: response.data });
      } else {
        set({
          error: response.message || "Failed to fetch upcoming activities",
        });
      }
    } catch (error: any) {
      set({ error: error.message || "Failed to fetch upcoming activities" });
    } finally {
      set({ isLoadingActivities: false });
    }
  },

  createActivity: async (data) => {
    set({ isLoadingActivities: true, error: null });
    try {
      const response = await CropService.createFarmingActivity(data);
      if (response.success && response.data) {
        const { activities } = get();
        set({ activities: [...activities, response.data] });
      } else {
        set({ error: response.message || "Failed to create activity" });
      }
    } catch (error: any) {
      set({ error: error.message || "Failed to create activity" });
    } finally {
      set({ isLoadingActivities: false });
    }
  },

  completeActivity: async (id, data) => {
    set({ isLoadingActivities: true, error: null });
    try {
      const response = await CropService.completeFarmingActivity(id, data);
      if (response.success && response.data) {
        const { activities } = get();
        set({
          activities: activities.map((activity) =>
            activity._id === id ? response.data! : activity
          ),
        });
      } else {
        set({ error: response.message || "Failed to complete activity" });
      }
    } catch (error: any) {
      set({ error: error.message || "Failed to complete activity" });
    } finally {
      set({ isLoadingActivities: false });
    }
  },

  setLoading: (isLoading) => set({ isLoading }),
  setError: (error) => set({ error }),
  clearError: () => set({ error: null }),

  // Update active crops based on current crop plans
  updateActiveCrops: () => {
    const { cropPlans } = get();
    // Transform crop plans into active crops format
    // This is where you'd filter for active plans and format them
    const activeCrops: ActiveCrop[] = cropPlans
      .filter((plan) => plan.status === "active") // Assuming there's a status field
      .map((plan) => ({
        id: plan._id,
        name: plan.cropName || plan.crop?.name || "Unknown Crop",
        stage: plan.currentStage || "Growing",
        progress: plan.progress || 0,
        image:
          plan.crop?.image ||
          "https://images.unsplash.com/photo-1592924357228-91a4daadcfea?w=100&h=100&fit=crop&crop=center",
      }));

    set({ activeCrops });
  },
}));
