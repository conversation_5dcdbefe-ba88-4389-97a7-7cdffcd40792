// User and Authentication Types
export interface User {
  id: string;
  firstName: string;
  lastName: string;
  email: string;
  role: "farmer" | "buyer";
  isEmailVerified: boolean;
  onboardingComplete: boolean;
  lastLogin?: string;
  createdAt?: string;
  updatedAt?: string;
  // Optional farmer-specific fields
  farmSize?: string;
  location?: string;
  cropTypes?: string[];
  landSize?: string;
  coordinates?: {
    latitude: number;
    longitude: number;
  };
  farmingExperience?: string;
  farmingMethods?: string[];
  certifications?: string[];
}

export interface AuthState {
  user: User | null;
  isAuthenticated: boolean;
  isLoading: boolean;
  error?: string;
}

// API Response Types
export interface ApiResponse<T = any> {
  success: boolean;
  message: string;
  data?: T;
  error?: string;
  errors?: Record<string, string>;
}

// Authentication Request Types
export interface SignupRequest {
  firstName: string;
  lastName: string;
  email: string;
  password: string;
  role: "farmer" | "buyer";
}

export interface LoginRequest {
  email: string;
  password: string;
}

export interface VerifyEmailRequest {
  email: string;
  code: string;
}

export interface FarmerDetailsRequest {
  landSize: string;
  farmSize: string;
  location: string;
  coordinates?: {
    latitude: number;
    longitude: number;
  };
  cropTypes: string[];
  farmingExperience?: string;
  farmingMethods?: string[];
  certifications?: string[];
}

// Crop Types
export interface Crop {
  _id: string;
  name: string;
  category: string;
  description?: string;
  image?: string;
  growthStages: {
    stage: string;
    duration: number;
    description: string;
  }[];
  requirements: {
    soilType: string[];
    climate: string[];
    waterRequirement: "low" | "medium" | "high";
    sunlightRequirement: "partial" | "full";
  };
  benefits: string[];
  marketInfo: {
    averagePrice: number;
    currency: string;
    demandLevel: "low" | "medium" | "high";
    exportPotential: boolean;
  };
  seasonInfo: {
    plantingMonths: number[];
    harvestMonths: number[];
    totalDuration: number;
  };
  suitableFor: {
    landSizes: string[];
    farmingExperience: string[];
    farmingMethods: string[];
  };
  isActive: boolean;
  createdAt: string;
  updatedAt: string;
}

export interface CropPlan {
  _id: string;
  farmerId: string;
  cropId: string;
  crop?: Crop;
  plantingDate: string;
  expectedHarvestDate: string;
  actualHarvestDate?: string;
  landArea: number;
  landUnit: "sqm" | "hectare" | "acre";
  currentStage: string;
  progress: number;
  status: "planned" | "planted" | "growing" | "harvested" | "failed";
  notes?: string;
  estimatedYield?: number;
  actualYield?: number;
  yieldUnit?: string;
  estimatedProfit?: number;
  actualProfit?: number;
  expenses: {
    seeds: number;
    fertilizer: number;
    pesticides: number;
    labor: number;
    equipment: number;
    other: number;
  };
  revenue?: number;
  createdAt: string;
  updatedAt: string;
}

export interface FarmingActivity {
  _id: string;
  farmerId: string;
  cropPlanId: string;
  cropPlan?: CropPlan;
  activityType:
    | "planting"
    | "fertilizing"
    | "watering"
    | "weeding"
    | "pest_control"
    | "harvesting"
    | "other";
  title: string;
  description?: string;
  scheduledDate: string;
  completedDate?: string;
  status: "scheduled" | "in_progress" | "completed" | "skipped" | "overdue";
  priority: "low" | "medium" | "high";
  reminderSent: boolean;
  notes?: string;
  cost?: number;
  createdAt: string;
  updatedAt: string;
}

export interface CropRecommendation {
  _id: string;
  farmerId: string;
  cropId: string;
  crop?: Crop;
  score: number;
  reasons: string[];
  estimatedProfit: number;
  riskLevel: "low" | "medium" | "high";
  seasonality: {
    bestPlantingMonth: number;
    bestHarvestMonth: number;
  };
  requirements: {
    landSizeNeeded: number;
    landUnit: string;
    investmentRequired: number;
  };
  marketOpportunity: {
    demandTrend: "increasing" | "stable" | "decreasing";
    competitionLevel: "low" | "medium" | "high";
    priceStability: "stable" | "volatile";
  };
  isActive: boolean;
  validUntil: string;
  createdAt: string;
  updatedAt: string;
}

// Legacy types for backward compatibility
export interface CropSuggestion {
  id: string;
  name: string;
  season: string;
  duration: string;
  yield: string;
  image: string;
  description: string;
}

// Request types
export interface CreateCropPlanRequest {
  cropId: string;
  plantingDate: string;
  landArea: number;
  landUnit: "sqm" | "hectare" | "acre";
  notes?: string;
}

export interface UpdateCropPlanRequest {
  currentStage?: string;
  progress?: number;
  status?: "planned" | "planted" | "growing" | "harvested" | "failed";
  notes?: string;
  actualHarvestDate?: string;
  actualYield?: number;
  yieldUnit?: string;
  actualProfit?: number;
  expenses?: {
    seeds?: number;
    fertilizer?: number;
    pesticides?: number;
    labor?: number;
    equipment?: number;
    other?: number;
  };
  revenue?: number;
}

export interface CreateFarmingActivityRequest {
  cropPlanId: string;
  activityType:
    | "planting"
    | "fertilizing"
    | "watering"
    | "weeding"
    | "pest_control"
    | "harvesting"
    | "other";
  title: string;
  description?: string;
  scheduledDate: string;
  priority?: "low" | "medium" | "high";
  cost?: number;
}

// Harvest Types
export interface Harvest {
  id: string;
  crop: string;
  quantity: string;
  condition: "excellent" | "good" | "fair" | "poor";
  declaredDate: string;
  expectedCollection: string;
  status: "scheduled" | "collected" | "pending" | "cancelled";
  agentName?: string;
  agentPhone?: string;
  estimatedPrice?: string;
  amount?: number;
  notes?: string;
}

// Marketplace Types
export interface Product {
  id: string;
  name: string;
  price: string;
  originalPrice?: string;
  image: string;
  rating: number;
  supplier: string;
  discount?: string;
  category: string;
  description?: string;
  unit?: string; // For output products (e.g., "per kg", "per bunch")
  type?: "input" | "output";
  stock?: number;
  currency?: string;
  tags?: string[];
  specifications?: {
    [key: string]: any;
  };
  supplierInfo?: {
    id: string;
    name: string;
    type: "verified" | "local_farmer" | "distributor";
    rating?: number;
    location?: string;
  };
  ratingInfo?: {
    average: number;
    count: number;
  };
  discountInfo?: {
    percentage: number;
    validUntil?: string;
    minQuantity?: number;
  };
  minOrderQuantity?: number;
  maxOrderQuantity?: number;
  isFeatured?: boolean;
  isActive?: boolean;
}

export interface Category {
  name: string;
  icon: string;
  count: string;
  type?: "input" | "output";
  description?: string;
  productCount?: number;
}

// Training Types
export interface Course {
  id: string;
  title: string;
  category: string;
  duration: string;
  level: string;
  featured: boolean;
  image: string;
  description?: string;
  completed?: boolean;
}
