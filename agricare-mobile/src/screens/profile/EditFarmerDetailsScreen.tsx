import React, { useState, useEffect } from "react";
import {
  View,
  Text,
  TextInput,
  TouchableOpacity,
  ScrollView,
  StyleSheet,
  KeyboardAvoidingView,
  Platform,
  ActivityIndicator,
} from "react-native";
import { SafeAreaView } from "react-native-safe-area-context";
import { Ionicons } from "@expo/vector-icons";
import { useNavigation } from "@react-navigation/native";
import { colors, commonStyles } from "../../utils/styles";
import { profileService } from "../../services/profileService";
import { useAlert } from "../../hooks/useAlert";
import Alert from "../../components/Alert";

export default function EditFarmerDetailsScreen() {
  const navigation = useNavigation();
  const { alert, showError, showSuccess, hideAlert } = useAlert();

  const [formData, setFormData] = useState({
    landSize: "",
    farmSize: "",
    location: "",
    cropTypes: [] as string[],
    farmingExperience: "",
    farmingMethods: [] as string[],
    certifications: [] as string[],
  });

  const [isLoading, setIsLoading] = useState(false);
  const [isLoadingData, setIsLoadingData] = useState(true);
  const [showCustomLandSize, setShowCustomLandSize] = useState(false);
  const [customLandSize, setCustomLandSize] = useState("");

  const cropOptions = [
    "Rice",
    "Wheat",
    "Corn",
    "Tomatoes",
    "Potatoes",
    "Onions",
    "Carrots",
    "Beans",
    "Lettuce",
    "Spinach",
    "Peppers",
    "Cucumbers",
  ];

  const landSizeOptions = [
    { value: "0.1", label: "Less than 0.1 acres" },
    { value: "0.5", label: "0.1 - 0.5 acres" },
    { value: "1", label: "0.5 - 1 acre" },
    { value: "2", label: "1 - 2 acres" },
    { value: "5", label: "2 - 5 acres" },
    { value: "10", label: "5 - 10 acres" },
    { value: "20", label: "10 - 20 acres" },
    { value: "50", label: "20 - 50 acres" },
    { value: "100", label: "50+ acres" },
    { value: "custom", label: "Custom size" },
  ];

  const farmSizeOptions = [
    { value: "backyard", label: "Backyard Garden (< 0.1 acres)" },
    { value: "small", label: "Small Farm (0.1 - 2 acres)" },
    { value: "medium", label: "Medium Farm (2 - 10 acres)" },
    { value: "large", label: "Large Farm (10+ acres)" },
  ];

  const experienceOptions = [
    { value: "beginner", label: "Beginner (0-2 years)" },
    { value: "intermediate", label: "Intermediate (3-5 years)" },
    { value: "experienced", label: "Experienced (6-10 years)" },
    { value: "expert", label: "Expert (10+ years)" },
  ];

  const methodOptions = [
    "organic",
    "conventional",
    "hydroponic",
    "permaculture",
    "sustainable",
  ];

  useEffect(() => {
    loadFarmerDetails();
  }, []);

  const loadFarmerDetails = async () => {
    try {
      const result = await profileService.getFarmerDetails();
      if (result.success && result.data) {
        const details = result.data.farmerDetails;
        const landSize = details.landSize || "";
        const isCustomLandSize =
          landSize &&
          !landSizeOptions.some(
            (option) =>
              option.value !== "custom" && landSize.includes(option.value)
          );

        setFormData({
          landSize: isCustomLandSize ? "custom" : landSize,
          farmSize: details.farmSize || "",
          location: details.location || "",
          cropTypes: details.cropTypes || [],
          farmingExperience: details.farmingExperience || "",
          farmingMethods: details.farmingMethods || [],
          certifications: details.certifications || [],
        });

        if (isCustomLandSize) {
          setShowCustomLandSize(true);
          setCustomLandSize(landSize);
        }
      }
    } catch (error) {
      console.log("No existing farmer details found");
    } finally {
      setIsLoadingData(false);
    }
  };

  const handleInputChange = (field: string, value: string) => {
    setFormData((prev) => ({ ...prev, [field]: value }));
  };

  const toggleArrayItem = (field: string, item: string) => {
    setFormData((prev) => ({
      ...prev,
      [field]: (prev[field as keyof typeof prev] as string[]).includes(item)
        ? (prev[field as keyof typeof prev] as string[]).filter(
            (i) => i !== item
          )
        : [...(prev[field as keyof typeof prev] as string[]), item],
    }));
  };

  const handleLandSizeChange = (value: string) => {
    if (value === "custom") {
      setShowCustomLandSize(true);
      setFormData((prev) => ({ ...prev, landSize: "custom" }));
    } else {
      setShowCustomLandSize(false);
      setCustomLandSize("");
      const selectedOption = landSizeOptions.find(
        (option) => option.value === value
      );
      setFormData((prev) => ({
        ...prev,
        landSize: selectedOption?.label || value,
      }));
    }
  };

  const validateForm = () => {
    if (!formData.landSize.trim()) {
      showError("Please select your total land size");
      return false;
    }

    if (formData.landSize === "custom" && !customLandSize.trim()) {
      showError("Please enter your custom land size");
      return false;
    }

    if (!formData.farmSize.trim()) {
      showError("Please enter your cultivated area");
      return false;
    }

    if (!formData.location.trim()) {
      showError("Please enter your farm location");
      return false;
    }

    if (formData.cropTypes.length === 0) {
      showError("Please select at least one crop type");
      return false;
    }

    return true;
  };

  const handleSave = async () => {
    if (!validateForm()) {
      return;
    }

    setIsLoading(true);

    try {
      const finalLandSize =
        formData.landSize === "custom"
          ? customLandSize.trim()
          : formData.landSize.trim();

      const result = await profileService.updateFarmerDetails({
        landSize: finalLandSize,
        farmSize: formData.farmSize.trim(),
        location: formData.location.trim(),
        cropTypes: formData.cropTypes,
        farmingExperience: formData.farmingExperience as any,
        farmingMethods: formData.farmingMethods,
        certifications: formData.certifications,
      });

      if (result.success) {
        showSuccess("Farm details updated successfully!");

        setTimeout(() => {
          navigation.goBack();
        }, 1500);
      } else {
        showError(result.message || "Failed to update farm details");
      }
    } catch (error: any) {
      console.error("Error updating farmer details:", error);
      showError("An unexpected error occurred. Please try again.");
    } finally {
      setIsLoading(false);
    }
  };

  if (isLoadingData) {
    return (
      <SafeAreaView style={styles.container}>
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color={colors.primary} />
          <Text style={styles.loadingText}>Loading farm details...</Text>
        </View>
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView style={styles.container}>
      <KeyboardAvoidingView
        style={styles.keyboardAvoidingView}
        behavior={Platform.OS === "ios" ? "padding" : "height"}
        keyboardVerticalOffset={Platform.OS === "ios" ? 0 : 20}
      >
        {/* Header */}
        <View style={styles.header}>
          <View style={styles.headerContent}>
            <TouchableOpacity onPress={() => navigation.goBack()}>
              <Ionicons name="arrow-back" size={24} color={colors.gray[600]} />
            </TouchableOpacity>
            <Text style={styles.headerTitle}>Farm Details</Text>
            <View style={styles.headerSpacer} />
          </View>
        </View>

        {/* Alert */}
        {alert && (
          <View style={styles.alertContainer}>
            <Alert
              type={alert.type}
              title={alert.title}
              message={alert.message}
              onClose={hideAlert}
            />
          </View>
        )}

        {/* Content */}
        <ScrollView
          style={styles.content}
          contentContainerStyle={styles.scrollContent}
          showsVerticalScrollIndicator={false}
          keyboardShouldPersistTaps="handled"
        >
          {/* Form */}
          <View style={styles.form}>
            {/* Land Size */}
            <View style={styles.inputGroup}>
              <Text style={styles.label}>Total Land Size</Text>
              <View style={styles.farmSizeContainer}>
                {landSizeOptions.map((option) => (
                  <TouchableOpacity
                    key={option.value}
                    onPress={() => handleLandSizeChange(option.value)}
                    style={[
                      styles.farmSizeOption,
                      formData.landSize === option.value &&
                        styles.farmSizeOptionSelected,
                    ]}
                  >
                    <View style={styles.farmSizeContent}>
                      <View
                        style={[
                          styles.farmSizeRadio,
                          formData.landSize === option.value &&
                            styles.farmSizeRadioSelected,
                        ]}
                      >
                        {formData.landSize === option.value && (
                          <View style={styles.farmSizeRadioInner} />
                        )}
                      </View>
                      <Text
                        style={[
                          styles.farmSizeText,
                          formData.landSize === option.value &&
                            styles.farmSizeTextSelected,
                        ]}
                      >
                        {option.label}
                      </Text>
                    </View>
                  </TouchableOpacity>
                ))}
              </View>

              {/* Custom Land Size Input */}
              {showCustomLandSize && (
                <View style={styles.customInputContainer}>
                  <Text style={styles.customInputLabel}>
                    Enter your land size:
                  </Text>
                  <View style={styles.inputContainer}>
                    <TextInput
                      value={customLandSize}
                      onChangeText={setCustomLandSize}
                      placeholder="e.g., 3.5 acres"
                      keyboardType="default"
                      style={[commonStyles.input, styles.inputWithUnit]}
                    />
                    <Text style={styles.unitText}>acres</Text>
                  </View>
                </View>
              )}
            </View>

            {/* Farm Size */}
            <View style={styles.inputGroup}>
              <Text style={styles.label}>Farm Size Category</Text>
              <View style={styles.farmSizeContainer}>
                {farmSizeOptions.map((option) => (
                  <TouchableOpacity
                    key={option.value}
                    onPress={() => handleInputChange("farmSize", option.value)}
                    style={[
                      styles.farmSizeOption,
                      formData.farmSize === option.value &&
                        styles.farmSizeOptionSelected,
                    ]}
                  >
                    <View style={styles.farmSizeContent}>
                      <View
                        style={[
                          styles.farmSizeRadio,
                          formData.farmSize === option.value &&
                            styles.farmSizeRadioSelected,
                        ]}
                      >
                        {formData.farmSize === option.value && (
                          <View style={styles.farmSizeRadioInner} />
                        )}
                      </View>
                      <Text
                        style={[
                          styles.farmSizeText,
                          formData.farmSize === option.value &&
                            styles.farmSizeTextSelected,
                        ]}
                      >
                        {option.label}
                      </Text>
                    </View>
                  </TouchableOpacity>
                ))}
              </View>
            </View>

            {/* Location */}
            <View style={styles.inputGroup}>
              <Text style={styles.label}>Farm Location</Text>
              <TextInput
                value={formData.location}
                onChangeText={(value) => handleInputChange("location", value)}
                placeholder="Enter your farm location"
                style={commonStyles.input}
                autoCapitalize="words"
              />
            </View>

            {/* Crop Types */}
            <View style={styles.inputGroup}>
              <Text style={styles.label}>Crop Types</Text>
              <Text style={styles.helperText}>Select all crops you grow</Text>
              <View style={styles.chipContainer}>
                {cropOptions.map((crop) => (
                  <TouchableOpacity
                    key={crop}
                    style={[
                      styles.chip,
                      formData.cropTypes.includes(crop) && styles.chipSelected,
                    ]}
                    onPress={() => toggleArrayItem("cropTypes", crop)}
                  >
                    <Text
                      style={[
                        styles.chipText,
                        formData.cropTypes.includes(crop) &&
                          styles.chipTextSelected,
                      ]}
                    >
                      {crop}
                    </Text>
                  </TouchableOpacity>
                ))}
              </View>
            </View>

            {/* Farming Experience */}
            <View style={styles.inputGroup}>
              <Text style={styles.label}>Farming Experience</Text>
              <View style={styles.farmSizeContainer}>
                {experienceOptions.map((option) => (
                  <TouchableOpacity
                    key={option.value}
                    onPress={() =>
                      handleInputChange("farmingExperience", option.value)
                    }
                    style={[
                      styles.farmSizeOption,
                      formData.farmingExperience === option.value &&
                        styles.farmSizeOptionSelected,
                    ]}
                  >
                    <View style={styles.farmSizeContent}>
                      <View
                        style={[
                          styles.farmSizeRadio,
                          formData.farmingExperience === option.value &&
                            styles.farmSizeRadioSelected,
                        ]}
                      >
                        {formData.farmingExperience === option.value && (
                          <View style={styles.farmSizeRadioInner} />
                        )}
                      </View>
                      <Text
                        style={[
                          styles.farmSizeText,
                          formData.farmingExperience === option.value &&
                            styles.farmSizeTextSelected,
                        ]}
                      >
                        {option.label}
                      </Text>
                    </View>
                  </TouchableOpacity>
                ))}
              </View>
            </View>
          </View>
        </ScrollView>

        {/* Save Button */}
        <View style={styles.footer}>
          <TouchableOpacity
            style={[
              commonStyles.button,
              styles.saveButton,
              isLoading && styles.disabledButton,
            ]}
            onPress={handleSave}
            disabled={isLoading}
          >
            {isLoading ? (
              <ActivityIndicator color={colors.white} size="small" />
            ) : (
              <>
                <Ionicons name="checkmark" size={20} color={colors.white} />
                <Text style={[commonStyles.buttonText, styles.saveButtonText]}>
                  Save Changes
                </Text>
              </>
            )}
          </TouchableOpacity>
        </View>
      </KeyboardAvoidingView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.background,
  },
  keyboardAvoidingView: {
    flex: 1,
  },
  loadingContainer: {
    flex: 1,
    alignItems: "center",
    justifyContent: "center",
    gap: 16,
  },
  loadingText: {
    fontSize: 16,
    color: colors.gray[600],
  },
  header: {
    backgroundColor: colors.white,
    borderBottomWidth: 1,
    borderBottomColor: colors.gray[200],
  },
  headerContent: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "space-between",
    paddingHorizontal: 16,
    paddingVertical: 12,
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: "600",
    color: colors.gray[900],
  },
  headerSpacer: {
    width: 24,
  },
  alertContainer: {
    paddingHorizontal: 16,
    paddingTop: 16,
  },
  content: {
    flex: 1,
    paddingHorizontal: 16,
  },
  scrollContent: {
    paddingVertical: 24,
  },
  titleContainer: {
    marginBottom: 32,
    alignItems: "center",
  },
  title: {
    fontSize: 24,
    fontWeight: "bold",
    color: colors.gray[900],
    marginBottom: 8,
    textAlign: "center",
  },
  subtitle: {
    fontSize: 16,
    color: colors.gray[600],
    textAlign: "center",
    lineHeight: 24,
  },
  form: {
    gap: 20,
  },
  inputGroup: {
    gap: 8,
  },
  label: {
    fontSize: 16,
    fontWeight: "500",
    color: colors.gray[900],
  },
  helperText: {
    fontSize: 12,
    color: colors.gray[500],
  },
  inputContainer: {
    flexDirection: "row",
    alignItems: "center",
  },
  inputWithUnit: {
    flex: 1,
    marginRight: 12,
  },
  unitText: {
    fontSize: 14,
    color: colors.gray[600],
    fontWeight: "500",
  },
  chipContainer: {
    flexDirection: "row",
    flexWrap: "wrap",
    gap: 8,
  },
  chip: {
    paddingHorizontal: 12,
    paddingVertical: 8,
    backgroundColor: colors.gray[100],
    borderRadius: 20,
    borderWidth: 1,
    borderColor: colors.gray[200],
  },
  chipSelected: {
    backgroundColor: colors.green[100],
    borderColor: colors.green[600],
  },
  chipText: {
    fontSize: 14,
    color: colors.gray[700],
  },
  chipTextSelected: {
    color: colors.green[700],
    fontWeight: "500",
  },
  radioContainer: {
    gap: 12,
  },
  radioOption: {
    flexDirection: "row",
    alignItems: "center",
    gap: 12,
  },
  radioCircle: {
    width: 20,
    height: 20,
    borderRadius: 10,
    borderWidth: 2,
    borderColor: colors.gray[300],
    alignItems: "center",
    justifyContent: "center",
  },
  radioSelected: {
    borderColor: colors.green[600],
  },
  radioInner: {
    width: 10,
    height: 10,
    borderRadius: 5,
    backgroundColor: colors.green[600],
  },
  radioText: {
    fontSize: 14,
    color: colors.gray[700],
    flex: 1,
  },
  footer: {
    paddingHorizontal: 16,
    paddingVertical: 16,
    backgroundColor: colors.white,
    borderTopWidth: 1,
    borderTopColor: colors.gray[200],
  },
  saveButton: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "center",
    gap: 8,
  },
  saveButtonText: {
    marginLeft: 0,
  },
  disabledButton: {
    opacity: 0.6,
  },
  // Farm Size Styles
  farmSizeContainer: {
    gap: 12,
  },
  farmSizeOption: {
    backgroundColor: colors.white,
    borderWidth: 1.5,
    borderColor: colors.gray[200],
    borderRadius: 12,
    padding: 16,
    shadowColor: colors.black,
    shadowOffset: {
      width: 0,
      height: 1,
    },
    shadowOpacity: 0.05,
    shadowRadius: 2,
    elevation: 1,
  },
  farmSizeOptionSelected: {
    borderColor: colors.primary,
    backgroundColor: colors.green[50],
    shadowColor: colors.primary,
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
  },
  farmSizeContent: {
    flexDirection: "row",
    alignItems: "center",
    gap: 12,
  },
  farmSizeRadio: {
    width: 20,
    height: 20,
    borderRadius: 10,
    borderWidth: 2,
    borderColor: colors.gray[300],
    alignItems: "center",
    justifyContent: "center",
    backgroundColor: colors.white,
  },
  farmSizeRadioSelected: {
    borderColor: colors.primary,
  },
  farmSizeRadioInner: {
    width: 10,
    height: 10,
    borderRadius: 5,
    backgroundColor: colors.primary,
  },
  farmSizeText: {
    fontSize: 15,
    color: colors.gray[700],
    fontWeight: "500",
    flex: 1,
    lineHeight: 20,
  },
  farmSizeTextSelected: {
    color: colors.primary,
    fontWeight: "600",
  },
  // Custom Input Styles
  customInputContainer: {
    marginTop: 16,
    padding: 16,
    backgroundColor: colors.green[50],
    borderRadius: 12,
    borderWidth: 1,
    borderColor: colors.green[200],
  },
  customInputLabel: {
    fontSize: 14,
    fontWeight: "500",
    color: colors.gray[700],
    marginBottom: 8,
  },
});
