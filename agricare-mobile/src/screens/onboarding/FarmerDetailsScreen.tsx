import React, { useState } from "react";
import {
  View,
  Text,
  TextInput,
  TouchableOpacity,
  ScrollView,
  Image,
  StyleSheet,
  KeyboardAvoidingView,
  Platform,
  ActivityIndicator,
} from "react-native";
import { StackNavigationProp } from "@react-navigation/stack";
import { Ionicons } from "@expo/vector-icons";
import { OnboardingStackParamList } from "../../navigation/OnboardingNavigator";
import { colors, commonStyles } from "../../utils/styles";
import { farmerService } from "../../services/farmerService";
import { useAlert } from "../../hooks/useAlert";
import Alert from "../../components/Alert";
import { useAuthStore } from "../../stores/authStore";

type FarmerDetailsScreenNavigationProp = StackNavigationProp<
  OnboardingStackParamList,
  "FarmerDetails"
>;

interface Props {
  navigation: FarmerDetailsScreenNavigationProp;
}

export default function FarmerDetailsScreen({ navigation }: Props) {
  const [formData, setFormData] = useState({
    landSize: "",
    farmSize: "",
    cropTypes: [] as string[],
    location: "",
    farmingExperience: "",
    farmingMethods: [] as string[],
  });

  const [showLocationPicker, setShowLocationPicker] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const { alert, showError, showSuccess, hideAlert } = useAlert();
  const { markOnboardingComplete } = useAuthStore();

  const cropOptions = [
    "Rice",
    "Wheat",
    "Corn",
    "Tomatoes",
    "Potatoes",
    "Onions",
    "Carrots",
    "Beans",
    "Lettuce",
    "Spinach",
    "Peppers",
    "Cucumbers",
  ];

  const farmSizeOptions = [
    { value: "backyard", label: "Backyard Garden (< 0.1 acres)" },
    { value: "small", label: "Small Farm (0.1 - 2 acres)" },
    { value: "medium", label: "Medium Farm (2 - 10 acres)" },
    { value: "large", label: "Large Farm (10+ acres)" },
  ];

  const experienceOptions = [
    { value: "beginner", label: "Beginner (0-2 years)" },
    { value: "intermediate", label: "Intermediate (3-5 years)" },
    { value: "experienced", label: "Experienced (6-10 years)" },
    { value: "expert", label: "Expert (10+ years)" },
  ];

  const methodOptions = [
    "organic",
    "conventional",
    "hydroponic",
    "permaculture",
    "sustainable",
  ];

  const handleInputChange = (field: string, value: string) => {
    setFormData((prev) => ({ ...prev, [field]: value }));
  };

  const toggleCrop = (crop: string) => {
    setFormData((prev) => ({
      ...prev,
      cropTypes: prev.cropTypes.includes(crop)
        ? prev.cropTypes.filter((c) => c !== crop)
        : [...prev.cropTypes, crop],
    }));
  };

  const toggleMethod = (method: string) => {
    setFormData((prev) => ({
      ...prev,
      farmingMethods: prev.farmingMethods.includes(method)
        ? prev.farmingMethods.filter((m) => m !== method)
        : [...prev.farmingMethods, method],
    }));
  };

  const validateForm = () => {
    if (!formData.landSize.trim()) {
      showError("Please enter your total land size");
      return false;
    }

    if (!formData.farmSize.trim()) {
      showError("Please select your farm size");
      return false;
    }

    if (!formData.farmingExperience.trim()) {
      showError("Please select your farming experience level");
      return false;
    }

    if (!formData.location.trim()) {
      showError("Please enter your farm location");
      return false;
    }

    if (formData.cropTypes.length === 0) {
      showError("Please select at least one crop type");
      return false;
    }

    return true;
  };

  const handleSubmit = async () => {
    if (!validateForm()) {
      return;
    }

    setIsLoading(true);

    try {
      const result = await farmerService.createOrUpdateFarmerDetails({
        landSize: formData.landSize.trim(),
        farmSize: formData.farmSize.trim(),
        location: formData.location.trim(),
        cropTypes: formData.cropTypes,
        farmingExperience: formData.farmingExperience as
          | "beginner"
          | "intermediate"
          | "experienced"
          | "expert",
        farmingMethods: formData.farmingMethods,
      });

      if (result.success) {
        showSuccess("Farm details saved successfully! Welcome to AgriCare!");

        // Mark onboarding as complete - this will trigger navigation to main app
        markOnboardingComplete();

        // The RootNavigator will automatically handle navigation to the main app
      } else {
        showError(result.message || "Failed to save farm details");
      }
    } catch (error: any) {
      showError("An unexpected error occurred. Please try again.");
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <View style={styles.container}>
      <KeyboardAvoidingView
        style={styles.keyboardAvoidingView}
        behavior={Platform.OS === "ios" ? "padding" : "height"}
        keyboardVerticalOffset={Platform.OS === "ios" ? 0 : 20}
      >
        {/* Header */}
        <View style={styles.header}>
          <View style={styles.headerContent}>
            <TouchableOpacity onPress={() => navigation.goBack()}>
              <Ionicons name="arrow-back" size={24} color={colors.gray[600]} />
            </TouchableOpacity>
            <Text style={styles.headerTitle}>Farm Details</Text>
            <View style={styles.headerSpacer} />
          </View>
        </View>

        {/* Content */}
        <ScrollView
          style={styles.content}
          contentContainerStyle={styles.scrollContent}
          showsVerticalScrollIndicator={false}
          keyboardShouldPersistTaps="handled"
        >
          {/* Illustration */}
          <View style={styles.imageContainer}>
            <Image
              source={{
                uri: "https://readdy.ai/api/search-image?query=icon%2C%203D%20cartoon%20farm%20landscape%20with%20fields%2C%20barn%2C%20and%20farming%20equipment%2C%20aerial%20view%20of%20organized%20farmland%2C%20vibrant%20green%20colors%20with%20soft%20gradients%2C%20minimalist%20design%2C%20smooth%20rounded%20shapes%2C%20subtle%20shading%2C%20no%20outlines%2C%20centered%20composition%2C%20isolated%20on%20white%20background%2C%20playful%20and%20friendly%20aesthetic%2C%20isometric%20perspective&width=128&height=128&seq=farm-details&orientation=squarish",
              }}
              style={styles.image}
              resizeMode="cover"
            />
          </View>

          {/* Title */}
          <View style={styles.titleContainer}>
            <Text style={styles.title}>Tell us about your farm</Text>
            <Text style={styles.subtitle}>
              Help us provide personalized recommendations
            </Text>
          </View>

          {/* Form */}
          <View style={styles.form}>
            {/* Land Size */}
            <View style={styles.inputGroup}>
              <Text style={styles.label}>Total Land Size</Text>
              <View style={styles.inputContainer}>
                <TextInput
                  value={formData.landSize}
                  onChangeText={(value) => handleInputChange("landSize", value)}
                  placeholder="Enter size in acres"
                  keyboardType="numeric"
                  style={[commonStyles.input, styles.inputWithUnit]}
                />
                <Text style={styles.unitText}>acres</Text>
              </View>
            </View>

            {/* Farm Size */}
            <View style={styles.inputGroup}>
              <Text style={styles.label}>Farm Size Category</Text>
              <View style={styles.farmSizeContainer}>
                {farmSizeOptions.map((option) => (
                  <TouchableOpacity
                    key={option.value}
                    onPress={() => handleInputChange("farmSize", option.value)}
                    style={[
                      styles.farmSizeOption,
                      formData.farmSize === option.value &&
                        styles.farmSizeOptionSelected,
                    ]}
                  >
                    <View style={styles.farmSizeContent}>
                      <View
                        style={[
                          styles.farmSizeRadio,
                          formData.farmSize === option.value &&
                            styles.farmSizeRadioSelected,
                        ]}
                      >
                        {formData.farmSize === option.value && (
                          <View style={styles.farmSizeRadioInner} />
                        )}
                      </View>
                      <Text
                        style={[
                          styles.farmSizeText,
                          formData.farmSize === option.value &&
                            styles.farmSizeTextSelected,
                        ]}
                      >
                        {option.label}
                      </Text>
                    </View>
                  </TouchableOpacity>
                ))}
              </View>
            </View>

            {/* Crop Types */}
            <View style={styles.inputGroup}>
              <Text style={styles.label}>What do you grow?</Text>
              <View style={styles.cropGrid}>
                {cropOptions.map((crop) => (
                  <TouchableOpacity
                    key={crop}
                    onPress={() => toggleCrop(crop)}
                    style={[
                      styles.cropButton,
                      formData.cropTypes.includes(crop) &&
                        styles.cropButtonActive,
                    ]}
                  >
                    <Text
                      style={[
                        styles.cropButtonText,
                        formData.cropTypes.includes(crop) &&
                          styles.cropButtonTextActive,
                      ]}
                    >
                      {crop}
                    </Text>
                  </TouchableOpacity>
                ))}
              </View>
            </View>

            {/* Farming Experience */}
            <View style={styles.inputGroup}>
              <Text style={styles.label}>Farming Experience</Text>
              <View style={styles.farmSizeContainer}>
                {experienceOptions.map((option) => (
                  <TouchableOpacity
                    key={option.value}
                    onPress={() =>
                      handleInputChange("farmingExperience", option.value)
                    }
                    style={[
                      styles.farmSizeOption,
                      formData.farmingExperience === option.value &&
                        styles.farmSizeOptionSelected,
                    ]}
                  >
                    <View style={styles.farmSizeContent}>
                      <View
                        style={[
                          styles.farmSizeRadio,
                          formData.farmingExperience === option.value &&
                            styles.farmSizeRadioSelected,
                        ]}
                      >
                        {formData.farmingExperience === option.value && (
                          <View style={styles.farmSizeRadioInner} />
                        )}
                      </View>
                      <Text
                        style={[
                          styles.farmSizeText,
                          formData.farmingExperience === option.value &&
                            styles.farmSizeTextSelected,
                        ]}
                      >
                        {option.label}
                      </Text>
                    </View>
                  </TouchableOpacity>
                ))}
              </View>
            </View>

            {/* Farming Methods */}
            <View style={styles.inputGroup}>
              <Text style={styles.label}>Farming Methods (Optional)</Text>
              <View style={styles.cropGrid}>
                {methodOptions.map((method) => (
                  <TouchableOpacity
                    key={method}
                    onPress={() => toggleMethod(method)}
                    style={[
                      styles.cropButton,
                      formData.farmingMethods.includes(method) &&
                        styles.cropButtonActive,
                    ]}
                  >
                    <Text
                      style={[
                        styles.cropButtonText,
                        formData.farmingMethods.includes(method) &&
                          styles.cropButtonTextActive,
                      ]}
                    >
                      {method.charAt(0).toUpperCase() + method.slice(1)}
                    </Text>
                  </TouchableOpacity>
                ))}
              </View>
            </View>

            {/* Location */}
            <View style={styles.inputGroup}>
              <Text style={styles.label}>Farm Location</Text>
              <View style={styles.inputContainer}>
                <View style={styles.inputIcon}>
                  <Ionicons
                    name="location-outline"
                    size={20}
                    color={colors.gray[400]}
                  />
                </View>
                <TextInput
                  value={formData.location}
                  onChangeText={(value) => handleInputChange("location", value)}
                  placeholder="Enter your farm address"
                  style={[commonStyles.input, styles.inputWithIcons]}
                />
                <TouchableOpacity
                  onPress={() => setShowLocationPicker(true)}
                  style={styles.inputIconRight}
                >
                  <Ionicons
                    name="navigate-outline"
                    size={20}
                    color={colors.primary}
                  />
                </TouchableOpacity>
              </View>
              <Text style={styles.helperText}>
                Tap the navigation icon to use GPS location
              </Text>
            </View>

            {/* Submit Button */}
            <TouchableOpacity
              onPress={handleSubmit}
              style={[commonStyles.button, isLoading && styles.buttonDisabled]}
              disabled={isLoading}
            >
              {isLoading ? (
                <ActivityIndicator color={colors.white} size="small" />
              ) : (
                <Text style={commonStyles.buttonText}>Continue</Text>
              )}
            </TouchableOpacity>

            {/* Skip Button */}
            <TouchableOpacity
              onPress={() => {
                // Show success message and mark onboarding complete
                showSuccess(
                  "Welcome to AgriCare! You can complete your farm details later from your profile."
                );
                // Mark onboarding as complete even if they skip farmer details
                markOnboardingComplete();
              }}
              style={styles.skipButton}
            >
              <Text style={styles.skipButtonText}>Skip for now</Text>
            </TouchableOpacity>
          </View>
        </ScrollView>
      </KeyboardAvoidingView>

      {/* Alert Component */}
      <Alert
        visible={alert.visible}
        type={alert.type}
        title={alert.title}
        message={alert.message}
        onClose={hideAlert}
        duration={alert.duration}
        position="top"
      />
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.green[50],
  },
  keyboardAvoidingView: {
    flex: 1,
  },
  header: {
    position: "absolute",
    top: 0,
    left: 0,
    right: 0,
    backgroundColor: "rgba(255, 255, 255, 1)",
    paddingHorizontal: 24,
    paddingVertical: 16,
    paddingTop: 50, // Account for status bar
    zIndex: 10,
  },
  headerContent: {
    flexDirection: "row",
    alignItems: "center",
  },
  headerTitle: {
    flex: 1,
    textAlign: "center",
    fontSize: 18,
    fontWeight: "600",
    color: colors.gray[800],
  },
  headerSpacer: {
    width: 24,
  },
  content: {
    flex: 1,
  },
  scrollContent: {
    flexGrow: 1,
    paddingTop: 100, // Account for fixed header
    paddingHorizontal: 24,
    paddingBottom: 40, // Extra padding for keyboard
  },
  imageContainer: {
    width: 128,
    height: 128,
    alignSelf: "center",
    marginBottom: 24,
    borderRadius: 12,
    overflow: "hidden",
  },
  image: {
    width: "100%",
    height: "100%",
  },
  titleContainer: {
    alignItems: "center",
    marginBottom: 40,
    marginTop: 40,
  },
  title: {
    fontSize: 28,
    fontWeight: "bold",
    color: colors.gray[800],
    marginBottom: 12,
    textAlign: "center",
  },
  subtitle: {
    fontSize: 16,
    color: colors.gray[600],
    textAlign: "center",
    lineHeight: 24,
  },
  form: {
    gap: 28,
  },
  inputGroup: {
    gap: 8,
  },
  label: {
    fontSize: 14,
    fontWeight: "500",
    color: colors.gray[700],
  },
  inputContainer: {
    position: "relative",
  },
  inputWithUnit: {
    paddingRight: 60,
  },
  inputWithIcons: {
    paddingLeft: 48,
    paddingRight: 48,
  },
  inputIcon: {
    position: "absolute",
    left: 16,
    top: 14,
    width: 20,
    height: 20,
    alignItems: "center",
    justifyContent: "center",
    zIndex: 1,
  },
  inputIconRight: {
    position: "absolute",
    right: 16,
    top: 14,
    width: 20,
    height: 20,
    alignItems: "center",
    justifyContent: "center",
    zIndex: 1,
  },
  unitText: {
    position: "absolute",
    right: 16,
    top: 14,
    fontSize: 14,
    color: colors.gray[500],
    lineHeight: 20,
  },
  cropGrid: {
    flexDirection: "row",
    flexWrap: "wrap",
    gap: 12,
  },
  cropButton: {
    paddingHorizontal: 12,
    paddingVertical: 8,
    borderRadius: 16,
    backgroundColor: colors.gray[100],
    borderWidth: 1,
    borderColor: colors.gray[200],
  },
  cropButtonActive: {
    backgroundColor: colors.primary,
    borderColor: colors.primary,
  },
  cropButtonText: {
    fontSize: 14,
    fontWeight: "500",
    color: colors.gray[700],
  },
  cropButtonTextActive: {
    color: colors.white,
  },
  helperText: {
    fontSize: 12,
    color: colors.gray[500],
    marginTop: 4,
  },
  skipButton: {
    backgroundColor: "transparent",
    paddingVertical: 12,
    paddingHorizontal: 24,
    borderRadius: 16,
    alignItems: "center",
    justifyContent: "center",
    marginTop: 12,
  },
  skipButtonText: {
    color: colors.gray[600],
    fontSize: 14,
    fontWeight: "500",
  },
  buttonDisabled: {
    opacity: 0.6,
  },
  // Farm Size Styles
  farmSizeContainer: {
    gap: 12,
  },
  farmSizeOption: {
    backgroundColor: colors.white,
    borderWidth: 1.5,
    borderColor: colors.gray[200],
    borderRadius: 12,
    padding: 16,
    shadowColor: colors.black,
    shadowOffset: {
      width: 0,
      height: 1,
    },
    shadowOpacity: 0.05,
    shadowRadius: 2,
    elevation: 1,
  },
  farmSizeOptionSelected: {
    borderColor: colors.primary,
    backgroundColor: colors.green[50],
    shadowColor: colors.primary,
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
  },
  farmSizeContent: {
    flexDirection: "row",
    alignItems: "center",
    gap: 12,
  },
  farmSizeRadio: {
    width: 20,
    height: 20,
    borderRadius: 10,
    borderWidth: 2,
    borderColor: colors.gray[300],
    alignItems: "center",
    justifyContent: "center",
    backgroundColor: colors.white,
  },
  farmSizeRadioSelected: {
    borderColor: colors.primary,
  },
  farmSizeRadioInner: {
    width: 10,
    height: 10,
    borderRadius: 5,
    backgroundColor: colors.primary,
  },
  farmSizeText: {
    fontSize: 15,
    color: colors.gray[700],
    fontWeight: "500",
    flex: 1,
    lineHeight: 20,
  },
  farmSizeTextSelected: {
    color: colors.primary,
    fontWeight: "600",
  },
});
