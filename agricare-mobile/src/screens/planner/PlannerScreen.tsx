import React, { useState, useEffect } from "react";
import {
  View,
  Text,
  TouchableOpacity,
  ScrollView,
  Image,
  StyleSheet,
  ActivityIndicator,
  Alert,
} from "react-native";
import { SafeAreaView } from "react-native-safe-area-context";
import { colors } from "../../utils/styles";
import { useCropStore } from "../../stores/cropStore";
import { useAlert } from "../../hooks/useAlert";

export default function PlannerScreen() {
  const [activeTab, setActiveTab] = useState("suggestions");
  const [currentMonth, setCurrentMonth] = useState(new Date().getMonth() + 1);
  const [currentYear, setCurrentYear] = useState(new Date().getFullYear());

  const {
    recommendations,
    cropPlans,
    calendarActivities,
    upcomingActivities,
    isLoading,
    isLoadingPlans,
    isLoadingActivities,
    isLoadingRecommendations,
    error,
    fetchRecommendations,
    fetchCropPlans,
    fetchCalendarView,
    fetchUpcomingActivities,
    createCropPlan,
    clearError,
  } = useCropStore();

  const { showSuccess, showError } = useAlert();

  useEffect(() => {
    // Load initial data
    loadData();
  }, []);

  const loadData = async () => {
    try {
      await Promise.all([
        fetchRecommendations({ limit: 10 }),
        fetchCropPlans({ status: undefined, limit: 20 }),
        fetchCalendarView(currentMonth, currentYear),
        fetchUpcomingActivities(7),
      ]);
    } catch (err) {
      console.error("Error loading planner data:", err);
    }
  };

  const handleAddToPlan = async (recommendation: any) => {
    try {
      const plantingDate = new Date();
      plantingDate.setDate(plantingDate.getDate() + 7); // Plant next week

      await createCropPlan({
        cropId: recommendation.cropId,
        plantingDate: plantingDate.toISOString(),
        landArea: 0.1, // Default to 0.1 hectare
        landUnit: "hectare",
        notes: `Added from recommendation: ${recommendation.reasons.join(
          ", "
        )}`,
      });

      showSuccess("Crop added to your plan successfully!", "Success");

      // Refresh crop plans
      fetchCropPlans({ status: undefined, limit: 20 });
    } catch (err) {
      showError("Failed to add crop to plan. Please try again.", "Error");
    }
  };

  const navigateMonth = (direction: "prev" | "next") => {
    let newMonth = currentMonth;
    let newYear = currentYear;

    if (direction === "next") {
      newMonth = currentMonth === 12 ? 1 : currentMonth + 1;
      newYear = currentMonth === 12 ? currentYear + 1 : currentYear;
    } else {
      newMonth = currentMonth === 1 ? 12 : currentMonth - 1;
      newYear = currentMonth === 1 ? currentYear - 1 : currentYear;
    }

    setCurrentMonth(newMonth);
    setCurrentYear(newYear);
    fetchCalendarView(newMonth, newYear);
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat("en-NG", {
      style: "currency",
      currency: "NGN",
      minimumFractionDigits: 0,
    }).format(amount);
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString("en-US", {
      day: "numeric",
      month: "short",
    });
  };

  const getMonthName = (month: number) => {
    return new Date(2024, month - 1).toLocaleDateString("en-US", {
      month: "long",
    });
  };

  // Convert calendar activities to events format
  const calendarEvents = Object.entries(calendarActivities).flatMap(
    ([date, activities]) =>
      activities.map((activity) => ({
        date: formatDate(activity.scheduledDate),
        task: activity.title,
        crop: activity.cropPlan?.crop?.name || "Unknown",
        type: activity.activityType,
      }))
  );

  // Show error alert if there's an error
  useEffect(() => {
    if (error) {
      showError(error, "Error");
      clearError();
    }
  }, [error]);

  const getEventTypeStyle = (type: string) => {
    switch (type) {
      case "planting":
        return { backgroundColor: colors.green[100] };
      case "fertilizing":
        return { backgroundColor: "#dbeafe" }; // blue-100
      case "harvesting":
        return { backgroundColor: "#fed7aa" }; // orange-100
      case "treatment":
        return { backgroundColor: "#e9d5ff" }; // purple-100
      default:
        return { backgroundColor: colors.gray[100] };
    }
  };

  const getEventTypeTextStyle = (type: string) => {
    switch (type) {
      case "planting":
        return { color: "#166534" }; // green-800
      case "fertilizing":
        return { color: "#1e40af" }; // blue-800
      case "harvesting":
        return { color: "#9a3412" }; // orange-800
      case "treatment":
        return { color: "#6b21a8" }; // purple-800
      default:
        return { color: colors.gray[800] };
    }
  };

  return (
    <SafeAreaView style={styles.container}>
      {/* Header */}
      <View style={styles.header}>
        <Text style={styles.headerTitle}>Crop Planner</Text>
      </View>

      {/* Tab Navigation */}
      <View style={styles.tabContainer}>
        <View style={styles.tabNavigation}>
          {[
            { id: "suggestions", label: "Suggestions" },
            { id: "calendar", label: "Calendar" },
            { id: "myplan", label: "My Plan" },
          ].map((tab) => (
            <TouchableOpacity
              key={tab.id}
              onPress={() => setActiveTab(tab.id)}
              style={[
                styles.tabButton,
                activeTab === tab.id && styles.tabButtonActive,
              ]}
            >
              <Text
                style={[
                  styles.tabText,
                  activeTab === tab.id && styles.tabTextActive,
                ]}
              >
                {tab.label}
              </Text>
            </TouchableOpacity>
          ))}
        </View>
      </View>

      <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
        {/* Crop Suggestions */}
        {activeTab === "suggestions" && (
          <View style={styles.section}>
            <View style={styles.sectionHeader}>
              <Text style={styles.sectionTitle}>Recommended for You</Text>
              <Text style={styles.sectionSubtitle}>Based on your profile</Text>
            </View>

            {isLoadingRecommendations ? (
              <View style={styles.loadingContainer}>
                <ActivityIndicator size="large" color={colors.primary} />
                <Text style={styles.loadingText}>
                  Loading recommendations...
                </Text>
              </View>
            ) : recommendations.length === 0 ? (
              <View style={styles.emptyContainer}>
                <Text style={styles.emptyText}>
                  No recommendations available. Complete your farmer profile to
                  get personalized suggestions.
                </Text>
              </View>
            ) : (
              recommendations.map((recommendation, index) => (
                <View
                  key={recommendation._id}
                  style={[
                    styles.cropCard,
                    recommendation.score >= 80 && styles.cropCardRecommended,
                  ]}
                >
                  <View style={styles.cropContent}>
                    <View style={styles.cropImageContainer}>
                      <Image
                        source={{
                          uri:
                            recommendation.crop?.image ||
                            "https://via.placeholder.com/120x120?text=No+Image",
                        }}
                        style={styles.cropImage}
                        resizeMode="cover"
                      />
                    </View>
                    <View style={styles.cropInfo}>
                      <View style={styles.cropHeader}>
                        <Text style={styles.cropName}>
                          {recommendation.crop?.name || "Unknown Crop"}
                        </Text>
                        {recommendation.score >= 80 && (
                          <View style={styles.recommendedBadge}>
                            <Text style={styles.recommendedText}>
                              Highly Recommended
                            </Text>
                          </View>
                        )}
                      </View>
                      <View style={styles.benefitsContainer}>
                        {recommendation.reasons.slice(0, 3).map((reason, i) => (
                          <View key={i} style={styles.benefitTag}>
                            <Text style={styles.benefitText}>{reason}</Text>
                          </View>
                        ))}
                      </View>
                      <View style={styles.cropStats}>
                        <Text style={styles.profitText}>
                          Est. Profit:{" "}
                          <Text style={styles.profitAmount}>
                            {formatCurrency(recommendation.estimatedProfit)}
                          </Text>
                        </Text>
                        <Text style={styles.seasonText}>
                          Duration:{" "}
                          {recommendation.crop?.seasonInfo.totalDuration || 0}{" "}
                          days
                        </Text>
                        <Text style={styles.riskText}>
                          Risk: {recommendation.riskLevel}
                        </Text>
                      </View>
                      <TouchableOpacity
                        style={styles.addButton}
                        onPress={() => handleAddToPlan(recommendation)}
                      >
                        <Text style={styles.addButtonText}>Add to Plan</Text>
                      </TouchableOpacity>
                    </View>
                  </View>
                </View>
              ))
            )}
          </View>
        )}

        {/* Calendar View */}
        {activeTab === "calendar" && (
          <View style={styles.section}>
            <View style={styles.calendarHeader}>
              <Text style={styles.sectionTitle}>
                {getMonthName(currentMonth)} {currentYear}
              </Text>
              <View style={styles.calendarNavigation}>
                <TouchableOpacity
                  style={styles.navButton}
                  onPress={() => navigateMonth("prev")}
                >
                  <Text style={styles.navButtonText}>‹</Text>
                </TouchableOpacity>
                <TouchableOpacity
                  style={styles.navButton}
                  onPress={() => navigateMonth("next")}
                >
                  <Text style={styles.navButtonText}>›</Text>
                </TouchableOpacity>
              </View>
            </View>

            {isLoadingActivities ? (
              <View style={styles.loadingContainer}>
                <ActivityIndicator size="large" color={colors.primary} />
                <Text style={styles.loadingText}>Loading activities...</Text>
              </View>
            ) : calendarEvents.length === 0 ? (
              <View style={styles.emptyContainer}>
                <Text style={styles.emptyText}>
                  No activities scheduled for this month.
                </Text>
              </View>
            ) : (
              calendarEvents.map((event, index) => (
                <View key={index} style={styles.eventCard}>
                  <View style={styles.eventContent}>
                    <View style={styles.eventDate}>
                      <Text style={styles.eventMonth}>
                        {event.date.split(" ")[1].toUpperCase()}
                      </Text>
                      <Text style={styles.eventDay}>
                        {event.date.split(" ")[0]}
                      </Text>
                    </View>
                    <View style={styles.eventInfo}>
                      <Text style={styles.eventTask}>{event.task}</Text>
                      <Text style={styles.eventCrop}>{event.crop}</Text>
                    </View>
                    <View
                      style={[styles.eventType, getEventTypeStyle(event.type)]}
                    >
                      <Text
                        style={[
                          styles.eventTypeText,
                          getEventTypeTextStyle(event.type),
                        ]}
                      >
                        {event.type}
                      </Text>
                    </View>
                  </View>
                </View>
              ))
            )}
          </View>
        )}

        {/* My Plan */}
        {activeTab === "myplan" && (
          <View style={styles.section}>
            <View style={styles.sectionHeader}>
              <Text style={styles.sectionTitle}>My Active Crops</Text>
              <TouchableOpacity onPress={() => setActiveTab("suggestions")}>
                <Text style={styles.addCropLink}>+ Add Crop</Text>
              </TouchableOpacity>
            </View>

            {isLoadingPlans ? (
              <View style={styles.loadingContainer}>
                <ActivityIndicator size="large" color={colors.primary} />
                <Text style={styles.loadingText}>Loading crop plans...</Text>
              </View>
            ) : cropPlans.length === 0 ? (
              <View style={styles.emptyContainer}>
                <Text style={styles.emptyText}>
                  No active crops yet. Add some crops from recommendations to
                  get started!
                </Text>
              </View>
            ) : (
              cropPlans
                .filter(
                  (plan) =>
                    plan.status !== "harvested" && plan.status !== "failed"
                )
                .map((plan) => (
                  <View key={plan._id} style={styles.myCropCard}>
                    <View style={styles.myCropContent}>
                      <View style={styles.myCropImageContainer}>
                        <Image
                          source={{
                            uri:
                              plan.crop?.image ||
                              "https://via.placeholder.com/80x80?text=No+Image",
                          }}
                          style={styles.myCropImage}
                          resizeMode="cover"
                        />
                      </View>
                      <View style={styles.myCropInfo}>
                        <View style={styles.myCropHeader}>
                          <Text style={styles.myCropName}>
                            {plan.crop?.name || "Unknown Crop"}
                          </Text>
                          <Text style={styles.myCropProgress}>
                            {plan.progress}%
                          </Text>
                        </View>
                        <Text style={styles.myCropStage}>
                          Stage: {plan.currentStage}
                        </Text>
                        <View style={styles.myCropDates}>
                          <Text style={styles.myCropDate}>
                            Planted: {formatDate(plan.plantingDate)}
                          </Text>
                          <Text style={styles.myCropDate}>
                            Harvest: {formatDate(plan.expectedHarvestDate)}
                          </Text>
                        </View>
                        <View style={styles.progressBarContainer}>
                          <View style={styles.progressBarBackground}>
                            <View
                              style={[
                                styles.progressBarFill,
                                { width: `${plan.progress}%` },
                              ]}
                            />
                          </View>
                        </View>
                      </View>
                    </View>
                  </View>
                ))
            )}
          </View>
        )}
      </ScrollView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.gray[50],
  },
  header: {
    backgroundColor: colors.white,
    borderBottomWidth: 1,
    borderBottomColor: colors.gray[200],
    paddingHorizontal: 16,
    paddingVertical: 12,
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: "600",
    color: colors.gray[900],
  },
  tabContainer: {
    paddingHorizontal: 16,
    paddingVertical: 16,
  },
  tabNavigation: {
    flexDirection: "row",
    backgroundColor: colors.gray[100],
    borderRadius: 24,
    padding: 4,
  },
  tabButton: {
    flex: 1,
    paddingVertical: 8,
    paddingHorizontal: 16,
    borderRadius: 20,
    alignItems: "center",
    justifyContent: "center",
  },
  tabButtonActive: {
    backgroundColor: colors.white,
    shadowColor: colors.black,
    shadowOffset: {
      width: 0,
      height: 1,
    },
    shadowOpacity: 0.05,
    shadowRadius: 2,
    elevation: 2,
  },
  tabText: {
    fontSize: 14,
    fontWeight: "500",
    color: colors.gray[600],
  },
  tabTextActive: {
    color: colors.primary,
  },
  content: {
    flex: 1,
    paddingHorizontal: 16,
  },
  section: {
    paddingBottom: 24,
  },
  sectionHeader: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "space-between",
    marginBottom: 16,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: "600",
    color: colors.gray[900],
  },
  sectionSubtitle: {
    fontSize: 14,
    color: colors.gray[600],
  },
  cropCard: {
    backgroundColor: colors.white,
    borderRadius: 16,
    padding: 16,
    marginBottom: 16,
    shadowColor: colors.black,
    shadowOffset: {
      width: 0,
      height: 1,
    },
    shadowOpacity: 0.05,
    shadowRadius: 2,
    elevation: 2,
    borderWidth: 1,
    borderColor: colors.gray[100],
  },
  cropCardRecommended: {
    borderColor: colors.green[100],
  },
  cropContent: {
    flexDirection: "row",
    alignItems: "flex-start",
  },
  cropImageContainer: {
    width: 80,
    height: 80,
    borderRadius: 12,
    overflow: "hidden",
    marginRight: 16,
  },
  cropImage: {
    width: "100%",
    height: "100%",
  },
  cropInfo: {
    flex: 1,
  },
  cropHeader: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "space-between",
    marginBottom: 8,
  },
  cropName: {
    fontSize: 16,
    fontWeight: "600",
    color: colors.gray[900],
  },
  recommendedBadge: {
    backgroundColor: colors.green[100],
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
  },
  recommendedText: {
    fontSize: 12,
    color: "#166534", // green-800
  },
  benefitsContainer: {
    flexDirection: "row",
    flexWrap: "wrap",
    gap: 4,
    marginBottom: 8,
  },
  benefitTag: {
    backgroundColor: colors.gray[100],
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
  },
  benefitText: {
    fontSize: 12,
    color: colors.gray[700],
  },
  cropStats: {
    flexDirection: "column",
    alignItems: "flex-start",
    justifyContent: "space-between",
    marginBottom: 12,
  },
  profitText: {
    fontSize: 14,
    color: colors.gray[600],
  },
  profitAmount: {
    fontWeight: "500",
    color: colors.primary,
  },
  seasonText: {
    fontSize: 14,
    color: colors.gray[600],
  },
  addButton: {
    backgroundColor: colors.primary,
    paddingVertical: 8,
    paddingHorizontal: 16,
    borderRadius: 12,
    alignItems: "center",
    justifyContent: "center",
  },
  addButtonText: {
    color: colors.white,
    fontSize: 14,
    fontWeight: "500",
  },
  // Calendar styles
  calendarHeader: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "space-between",
    marginBottom: 16,
  },
  calendarNavigation: {
    flexDirection: "row",
    gap: 8,
  },
  navButton: {
    width: 32,
    height: 32,
    alignItems: "center",
    justifyContent: "center",
  },
  navButtonText: {
    fontSize: 18,
    color: colors.gray[600],
  },
  eventCard: {
    backgroundColor: colors.white,
    borderRadius: 16,
    padding: 16,
    marginBottom: 12,
    shadowColor: colors.black,
    shadowOffset: {
      width: 0,
      height: 1,
    },
    shadowOpacity: 0.05,
    shadowRadius: 2,
    elevation: 2,
    borderWidth: 1,
    borderColor: colors.gray[100],
  },
  eventContent: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "space-between",
  },
  eventDate: {
    alignItems: "center",
    marginRight: 12,
  },
  eventMonth: {
    fontSize: 12,
    color: colors.gray[600],
    fontWeight: "500",
    letterSpacing: 0.5,
  },
  eventDay: {
    fontSize: 18,
    fontWeight: "600",
    color: colors.gray[900],
  },
  eventInfo: {
    flex: 1,
  },
  eventTask: {
    fontSize: 16,
    fontWeight: "500",
    color: colors.gray[900],
  },
  eventCrop: {
    fontSize: 14,
    color: colors.gray[600],
  },
  eventType: {
    paddingHorizontal: 12,
    paddingVertical: 4,
    borderRadius: 12,
  },
  eventTypeText: {
    fontSize: 12,
    fontWeight: "500",
  },
  // My Plan styles
  addCropLink: {
    fontSize: 14,
    fontWeight: "500",
    color: colors.primary,
  },
  myCropCard: {
    backgroundColor: colors.white,
    borderRadius: 16,
    padding: 16,
    marginBottom: 16,
    shadowColor: colors.black,
    shadowOffset: {
      width: 0,
      height: 1,
    },
    shadowOpacity: 0.05,
    shadowRadius: 2,
    elevation: 2,
    borderWidth: 1,
    borderColor: colors.gray[100],
  },
  myCropContent: {
    flexDirection: "row",
    alignItems: "center",
  },
  myCropImageContainer: {
    width: 64,
    height: 64,
    borderRadius: 12,
    overflow: "hidden",
    marginRight: 16,
  },
  myCropImage: {
    width: "100%",
    height: "100%",
  },
  myCropInfo: {
    flex: 1,
  },
  myCropHeader: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "space-between",
    marginBottom: 8,
  },
  myCropName: {
    fontSize: 16,
    fontWeight: "600",
    color: colors.gray[900],
  },
  myCropProgress: {
    fontSize: 14,
    fontWeight: "500",
    color: colors.primary,
  },
  myCropStage: {
    fontSize: 14,
    color: colors.gray[600],
    marginBottom: 8,
  },
  myCropDates: {
    flexDirection: "row",
    justifyContent: "space-between",
    marginBottom: 8,
  },
  myCropDate: {
    fontSize: 12,
    color: colors.gray[600],
  },
  progressBarContainer: {
    width: "100%",
  },
  progressBarBackground: {
    width: "100%",
    height: 8,
    backgroundColor: colors.gray[200],
    borderRadius: 4,
    overflow: "hidden",
  },
  progressBarFill: {
    height: "100%",
    backgroundColor: colors.primary,
    borderRadius: 4,
  },
  // Loading and empty states
  loadingContainer: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
    paddingVertical: 40,
  },
  loadingText: {
    marginTop: 12,
    fontSize: 14,
    color: colors.gray[600],
  },
  emptyContainer: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
    paddingVertical: 40,
    paddingHorizontal: 20,
  },
  emptyText: {
    fontSize: 14,
    color: colors.gray[600],
    textAlign: "center",
    lineHeight: 20,
  },
  riskText: {
    fontSize: 14,
    color: colors.gray[600],
    textTransform: "capitalize",
  },
});
