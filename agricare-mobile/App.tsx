import React, { useEffect, useState } from "react";
import { NavigationContainer } from "@react-navigation/native";
import { StatusBar } from "expo-status-bar";
import { SafeAreaProvider } from "react-native-safe-area-context";
import RootNavigator from "./src/navigation/RootNavigator";
import { useAuthStore } from "./src/stores/authStore";
import LoadingScreen from "./src/components/LoadingScreen";

export default function App() {
  const [isInitializing, setIsInitializing] = useState(true);
  const { initializeApp } = useAuthStore();

  useEffect(() => {
    const initialize = async () => {
      try {
        // Check if user is already authenticated
        await initializeApp();
      } catch (error) {
        console.error("Error initializing app:", error);
      } finally {
        setIsInitializing(false);
      }
    };

    initialize();
  }, []); // Empty dependency array - only run once on mount

  // Show loading screen while checking authentication
  if (isInitializing) {
    return (
      <SafeAreaProvider>
        <LoadingScreen message="Checking authentication..." />
        <StatusBar style="auto" />
      </SafeAreaProvider>
    );
  }

  return (
    <SafeAreaProvider>
      <NavigationContainer>
        <RootNavigator />
        <StatusBar style="auto" />
      </NavigationContainer>
    </SafeAreaProvider>
  );
}
