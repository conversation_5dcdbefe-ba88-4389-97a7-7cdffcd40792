const mongoose = require('mongoose');

mongoose.connect('mongodb://localhost:27017/agricare').then(async () => {
  const CropRecommendation = mongoose.model('CropRecommendation', new mongoose.Schema({}, { strict: false }));
  
  const result = await CropRecommendation.deleteMany({ farmerId: '6880372d0994a4a952cfd837' });
  console.log('Cleared', result.deletedCount, 'existing recommendations');
  
  mongoose.disconnect();
}).catch(console.error);
