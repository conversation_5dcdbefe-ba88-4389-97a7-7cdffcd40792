const mongoose = require('mongoose');

// Connect to MongoDB
mongoose.connect('mongodb://localhost:27017/agricare').then(async () => {
  console.log('Connected to MongoDB');
  
  const FarmerDetails = mongoose.model('FarmerDetails', new mongoose.Schema({}, { strict: false }));
  
  // Find the farmer details for the logged-in user
  const farmerDetails = await FarmerDetails.findOne({ userId: '6880372d0994a4a952cfd837' });
  
  if (farmerDetails) {
    console.log('Current farmer details:', {
      farmSize: farmerDetails.farmSize,
      farmingExperience: farmerDetails.farmingExperience,
      cropTypes: farmerDetails.cropTypes
    });
    
    // Update with correct values
    farmerDetails.farmSize = 'large'; // Convert "30" to "large"
    farmerDetails.farmingExperience = 'intermediate'; // Add missing experience level
    
    await farmerDetails.save();
    
    console.log('Updated farmer details:', {
      farmSize: farmerDetails.farmSize,
      farmingExperience: farmerDetails.farmingExperience,
      cropTypes: farmerDetails.cropTypes
    });
  } else {
    console.log('No farmer details found');
  }
  
  mongoose.disconnect();
}).catch(console.error);
