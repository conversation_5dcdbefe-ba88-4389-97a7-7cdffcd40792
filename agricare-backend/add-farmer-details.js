const mongoose = require('mongoose');

// Connect to MongoDB
mongoose.connect('mongodb://localhost:27017/agricare').then(async () => {
  console.log('Connected to MongoDB');
  
  const User = mongoose.model('User', new mongoose.Schema({}, { strict: false }));
  const FarmerDetails = mongoose.model('FarmerDetails', new mongoose.Schema({}, { strict: false }));
  
  const user = await User.findOne({ email: '<EMAIL>' });
  console.log('User found:', user?.email, user?.role, user?._id);
  
  if (user) {
    // Delete existing farmer details
    await FarmerDetails.deleteOne({ userId: user._id });
    
    const farmerDetails = new FarmerDetails({
      userId: user._id,
      landSize: '1.5 hectares',
      farmSize: 'medium',
      location: 'Lagos, Nigeria',
      coordinates: {
        latitude: 6.5244,
        longitude: 3.3792,
      },
      cropTypes: ['vegetables', 'grains'],
      farmingExperience: 'intermediate',
      farmingMethods: ['conventional', 'organic'],
      certifications: [],
    });
    
    const saved = await farmerDetails.save();
    console.log('Farmer details created:', saved.farmingExperience);
  }
  
  mongoose.disconnect();
}).catch(console.error);
