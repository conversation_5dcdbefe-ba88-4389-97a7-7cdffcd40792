import dotenv from 'dotenv';
dotenv.config();

import { connectDatabase } from '../config/database';
import { User, FarmerDetails } from '../models';
import { logger } from '../utils/logger';
import bcrypt from 'bcryptjs';

async function createTestUser() {
  try {
    await connectDatabase();

    // Clear existing test user
    await User.deleteOne({ email: '<EMAIL>' });
    await FarmerDetails.deleteOne({ userId: { $exists: true } });
    logger.info('Cleared existing test user');

    // Create test user
    const hashedPassword = await bcrypt.hash('password123', 12);
    const testUser = new User({
      firstName: 'Test',
      lastName: 'Farmer',
      email: '<EMAIL>',
      password: hashedPassword,
      role: 'farmer',
      isEmailVerified: true,
      onboardingComplete: true,
    });

    const savedUser = await testUser.save();
    logger.info('Created test user:', savedUser.email);

    // Create farmer details
    const farmerDetails = new FarmerDetails({
      userId: savedUser._id,
      landSize: '2 hectares',
      farmSize: 'medium',
      location: 'Lagos, Nigeria',
      coordinates: {
        latitude: 6.5244,
        longitude: 3.3792,
      },
      cropTypes: ['vegetables', 'grains'],
      farmingExperience: 'intermediate',
      farmingMethods: ['conventional', 'organic'],
      certifications: [],
    });

    const savedFarmerDetails = await farmerDetails.save();
    logger.info('Created farmer details for user:', savedFarmerDetails.userId);

    logger.info('Test user creation completed successfully');
    logger.info('Login credentials: <EMAIL> / password123');
    process.exit(0);
  } catch (error) {
    logger.error('Error creating test user:', error);
    process.exit(1);
  }
}

if (require.main === module) {
  createTestUser();
}
