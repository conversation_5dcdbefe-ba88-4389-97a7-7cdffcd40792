import { Request, Response } from 'express';
import { FarmerDetails, User } from '@/models';
import { AppError } from '@/types';
import { asyncHandler } from '@/middleware/errorHandler';

/**
 * Create or update farmer details
 */
export const createOrUpdateFarmerDetails = asyncHandler(async (req: Request, res: Response) => {
  const userId = req.user._id;
  const { landSize, farmSize, location, coordinates, cropTypes, farmingExperience, farmingMethods, certifications } =
    req.body;

  // Check if user is a farmer
  if (req.user.role !== 'farmer') {
    throw new AppError('Only farmers can create farmer details', 403);
  }

  // Check if farmer details already exist
  let farmerDetails = await FarmerDetails.findOne({ userId });

  if (farmerDetails) {
    // Update existing details
    farmerDetails = await FarmerDetails.findOneAndUpdate(
      { userId },
      {
        landSize,
        farmSize,
        location,
        coordinates,
        cropTypes,
        farmingExperience,
        farmingMethods,
        certifications,
      },
      { new: true, runValidators: true }
    );
  } else {
    // Create new farmer details
    farmerDetails = new FarmerDetails({
      userId,
      landSize,
      farmSize,
      location,
      coordinates,
      cropTypes,
      farmingExperience,
      farmingMethods,
      certifications,
    });
    await farmerDetails.save();
  }

  // Mark farmer's onboarding as complete
  await User.findByIdAndUpdate(userId, { onboardingComplete: true });

  // Populate user data
  if (farmerDetails) {
    await farmerDetails.populate('user', 'name email role isEmailVerified');
  }

  res.status(200).json({
    success: true,
    message: 'Farmer details saved successfully',
    data: { farmerDetails },
  });
});

/**
 * Get farmer details by user ID
 */
export const getFarmerDetails = asyncHandler(async (req: Request, res: Response) => {
  const userId = req.user._id;

  const farmerDetails = await FarmerDetails.findOne({ userId }).populate('user', 'name email role isEmailVerified');

  if (!farmerDetails) {
    throw new AppError('Farmer details not found', 404);
  }

  res.status(200).json({
    success: true,
    message: 'Farmer details retrieved successfully',
    data: { farmerDetails },
  });
});

/**
 * Get all farmers with pagination and filters
 */
export const getAllFarmers = asyncHandler(async (req: Request, res: Response) => {
  const {
    page = 1,
    limit = 10,
    location,
    cropTypes,
    farmingExperience,
    farmingMethods,
    sort = 'createdAt',
    order = 'desc',
  } = req.query;

  // Build filter
  const filter: any = {};
  if (location) {
    filter.location = { $regex: location, $options: 'i' };
  }
  if (cropTypes) {
    const crops = Array.isArray(cropTypes) ? cropTypes : [cropTypes];
    filter.cropTypes = { $in: crops };
  }
  if (farmingExperience) {
    filter.farmingExperience = farmingExperience;
  }
  if (farmingMethods) {
    const methods = Array.isArray(farmingMethods) ? farmingMethods : [farmingMethods];
    filter.farmingMethods = { $in: methods };
  }

  // Calculate pagination
  const skip = (Number(page) - 1) * Number(limit);

  // Build sort object
  const sortObj: any = {};
  sortObj[sort as string] = order === 'asc' ? 1 : -1;

  // Get farmers with pagination
  const farmers = await FarmerDetails.find(filter)
    .populate('user', 'name email isEmailVerified createdAt')
    .sort(sortObj)
    .skip(skip)
    .limit(Number(limit));

  // Get total count
  const total = await FarmerDetails.countDocuments(filter);

  res.status(200).json({
    success: true,
    message: 'Farmers retrieved successfully',
    data: {
      farmers,
      pagination: {
        page: Number(page),
        limit: Number(limit),
        total,
        pages: Math.ceil(total / Number(limit)),
        hasNext: Number(page) * Number(limit) < total,
        hasPrev: Number(page) > 1,
      },
    },
  });
});

/**
 * Get farmer details by farmer ID
 */
export const getFarmerById = asyncHandler(async (req: Request, res: Response) => {
  const { farmerId } = req.params;

  const farmerDetails = await FarmerDetails.findById(farmerId).populate(
    'user',
    'name email role isEmailVerified createdAt'
  );

  if (!farmerDetails) {
    throw new AppError('Farmer not found', 404);
  }

  res.status(200).json({
    success: true,
    message: 'Farmer details retrieved successfully',
    data: { farmerDetails },
  });
});

/**
 * Search farmers by location and crop types
 */
export const searchFarmers = asyncHandler(async (req: Request, res: Response) => {
  const {
    location,
    cropTypes,
    radius = 50, // km
    latitude,
    longitude,
    page = 1,
    limit = 10,
  } = req.query;

  const filter: any = {};

  // Location-based search
  if (location) {
    filter.location = { $regex: location, $options: 'i' };
  }

  // Crop types search
  if (cropTypes) {
    const crops = Array.isArray(cropTypes) ? cropTypes : [cropTypes];
    filter.cropTypes = { $in: crops };
  }

  // Geospatial search if coordinates provided
  if (latitude && longitude) {
    filter['coordinates'] = {
      $near: {
        $geometry: {
          type: 'Point',
          coordinates: [Number(longitude), Number(latitude)],
        },
        $maxDistance: Number(radius) * 1000, // Convert km to meters
      },
    };
  }

  // Calculate pagination
  const skip = (Number(page) - 1) * Number(limit);

  // Search farmers
  const farmers = await FarmerDetails.find(filter)
    .populate('user', 'name email isEmailVerified')
    .skip(skip)
    .limit(Number(limit));

  // Get total count
  const total = await FarmerDetails.countDocuments(filter);

  res.status(200).json({
    success: true,
    message: 'Farmers search completed successfully',
    data: {
      farmers,
      searchCriteria: {
        location,
        cropTypes,
        radius: Number(radius),
        coordinates: latitude && longitude ? { latitude, longitude } : null,
      },
      pagination: {
        page: Number(page),
        limit: Number(limit),
        total,
        pages: Math.ceil(total / Number(limit)),
        hasNext: Number(page) * Number(limit) < total,
        hasPrev: Number(page) > 1,
      },
    },
  });
});

/**
 * Get farmer statistics
 */
export const getFarmerStats = asyncHandler(async (_req: Request, res: Response) => {
  const stats = await FarmerDetails.aggregate([
    {
      $group: {
        _id: null,
        totalFarmers: { $sum: 1 },
        avgLandSize: { $avg: '$landSize' },
        cropTypesDistribution: { $push: '$cropTypes' },
        experienceDistribution: {
          $push: '$farmingExperience',
        },
        methodsDistribution: {
          $push: '$farmingMethods',
        },
      },
    },
    {
      $project: {
        _id: 0,
        totalFarmers: 1,
        avgLandSize: { $round: ['$avgLandSize', 2] },
        cropTypesDistribution: 1,
        experienceDistribution: 1,
        methodsDistribution: 1,
      },
    },
  ]);

  // Get location distribution
  const locationStats = await FarmerDetails.aggregate([
    {
      $group: {
        _id: '$location',
        count: { $sum: 1 },
      },
    },
    {
      $sort: { count: -1 },
    },
    {
      $limit: 10,
    },
  ]);

  res.status(200).json({
    success: true,
    message: 'Farmer statistics retrieved successfully',
    data: {
      overview: stats[0] || {},
      topLocations: locationStats,
    },
  });
});

/**
 * Delete farmer details
 */
export const deleteFarmerDetails = asyncHandler(async (req: Request, res: Response) => {
  const userId = req.user._id;

  const farmerDetails = await FarmerDetails.findOneAndDelete({ userId });
  if (!farmerDetails) {
    throw new AppError('Farmer details not found', 404);
  }

  res.status(200).json({
    success: true,
    message: 'Farmer details deleted successfully',
  });
});
