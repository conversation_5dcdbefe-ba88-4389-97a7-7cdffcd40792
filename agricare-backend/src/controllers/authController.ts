import { Request, Response } from 'express';
import { User, VerificationCode } from '@/models';
import { JWTService } from '@/utils/jwt';
import { emailService } from '@/services/emailService';
import { AppError } from '@/types';
import { logger } from '@/utils/logger';
import { asyncHand<PERSON> } from '@/middleware/errorHandler';

/**
 * Register a new user
 */
export const signup = asyncHandler(async (req: Request, res: Response) => {
  const { firstName, lastName, email, password, role } = req.body;

  // Check if user already exists
  const existingUser = await User.findOne({ email });
  if (existingUser) {
    throw new AppError('User with this email already exists', 400);
  }

  // Create new user
  const user = new User({
    firstName,
    lastName,
    email,
    password,
    role,
  });

  await user.save();

  // Generate tokens immediately for login
  const tokens = JWTService.generateTokens({
    userId: user._id,
    email: user.email,
    role: user.role,
  });

  // Send response immediately without waiting for email
  res.status(201).json({
    success: true,
    message: 'User registered and logged in successfully. A verification email has been sent.',
    data: {
      user: {
        id: user._id,
        firstName: user.firstName,
        lastName: user.lastName,
        email: user.email,
        role: user.role,
        isEmailVerified: user.isEmailVerified,
        onboardingComplete: user.onboardingComplete,
      },
      ...tokens,
    },
  });

  // Send verification email asynchronously (fire and forget)
  setImmediate(async () => {
    try {
      // Generate verification code
      const verificationCode = await VerificationCode.createCode(email, 'email_verification');

      // Send verification email
      await emailService.sendVerificationEmail(email, firstName, verificationCode.code);
      logger.info(`Verification email sent successfully to: ${email}`);
    } catch (error) {
      logger.error('Failed to send verification email:', error);
      // Email failure doesn't affect user registration/login
    }
  });
});

/**
 * Login user
 */
export const login = asyncHandler(async (req: Request, res: Response) => {
  const { email, password } = req.body;

  // Find user and include password for comparison
  const user = await User.findOne({ email }).select('+password');
  if (!user) {
    throw new AppError('Invalid email or password', 401);
  }

  // Check if user is active
  if (!user.isActive) {
    throw new AppError('Account has been deactivated', 401);
  }

  // Compare password
  const isPasswordValid = await user.comparePassword(password);
  if (!isPasswordValid) {
    throw new AppError('Invalid email or password', 401);
  }

  // Update last login
  user.lastLogin = new Date();
  await user.save();

  // Generate tokens
  const tokens = JWTService.generateTokens({
    userId: user._id,
    email: user.email,
    role: user.role,
  });

  res.status(200).json({
    success: true,
    message: 'Login successful',
    data: {
      user: {
        id: user._id,
        firstName: user.firstName,
        lastName: user.lastName,
        email: user.email,
        role: user.role,
        isEmailVerified: user.isEmailVerified,
        onboardingComplete: user.onboardingComplete,
        lastLogin: user.lastLogin,
      },
      ...tokens,
    },
  });
});

/**
 * Verify email address
 */
export const verifyEmail = asyncHandler(async (req: Request, res: Response): Promise<void> => {
  const { email, code } = req.body;

  // Find user
  const user = await User.findOne({ email });
  if (!user) {
    throw new AppError('User not found', 404);
  }

  if (user.isEmailVerified) {
    res.status(200).json({
      success: true,
      message: 'Email is already verified',
    });
    return;
  }

  // Verify the code
  const verification = await VerificationCode.verifyCode(email, code, 'email_verification');
  if (!verification.success) {
    // Increment attempts for invalid codes
    await VerificationCode.incrementAttempts(email, code, 'email_verification');
    throw new AppError(verification.message, 400);
  }

  // Mark email as verified
  user.isEmailVerified = true;
  user.emailVerificationToken = undefined;
  user.emailVerificationExpires = undefined;

  // For buyers, mark onboarding as complete since they don't need farmer details
  if (user.role === 'buyer') {
    user.onboardingComplete = true;
  }

  await user.save();

  // Send welcome email
  try {
    await emailService.sendWelcomeEmail(user.email, user.firstName, user.role);
  } catch (error) {
    logger.error('Failed to send welcome email:', error);
  }

  res.status(200).json({
    success: true,
    message: 'Email verified successfully',
    data: {
      user: {
        id: user._id,
        firstName: user.firstName,
        lastName: user.lastName,
        email: user.email,
        role: user.role,
        isEmailVerified: user.isEmailVerified,
        onboardingComplete: user.onboardingComplete,
      },
    },
  });
});

/**
 * Resend verification email
 */
export const resendVerification = asyncHandler(async (req: Request, res: Response): Promise<void> => {
  const { email } = req.body;

  // Find user
  const user = await User.findOne({ email });
  if (!user) {
    throw new AppError('User not found', 404);
  }

  if (user.isEmailVerified) {
    res.status(200).json({
      success: true,
      message: 'Email is already verified',
    });
    return;
  }

  // Generate new verification code
  const verificationCode = await VerificationCode.createCode(email, 'email_verification');

  // Send verification email
  try {
    await emailService.sendVerificationEmail(email, user.firstName, verificationCode.code);
  } catch (error) {
    logger.error('Failed to send verification email:', error);
    throw new AppError('Failed to send verification email', 500);
  }

  res.status(200).json({
    success: true,
    message: 'Verification email sent successfully',
  });
});

/**
 * Forgot password
 */
export const forgotPassword = asyncHandler(async (req: Request, res: Response): Promise<void> => {
  const { email } = req.body;

  // Find user
  const user = await User.findOne({ email });
  if (!user) {
    // Don't reveal if user exists or not
    res.status(200).json({
      success: true,
      message: 'If an account with this email exists, a password reset code has been sent.',
    });
    return;
  }

  // Generate password reset code
  const resetCode = await VerificationCode.createCode(email, 'password_reset');

  // Send password reset email
  try {
    await emailService.sendPasswordResetEmail(email, user.firstName, resetCode.code);
  } catch (error) {
    logger.error('Failed to send password reset email:', error);
    throw new AppError('Failed to send password reset email', 500);
  }

  res.status(200).json({
    success: true,
    message: 'If an account with this email exists, a password reset code has been sent.',
  });
});

/**
 * Reset password
 */
export const resetPassword = asyncHandler(async (req: Request, res: Response) => {
  const { email, code, newPassword } = req.body;

  // Find user
  const user = await User.findOne({ email });
  if (!user) {
    throw new AppError('Invalid reset code', 400);
  }

  // Verify the reset code
  const verification = await VerificationCode.verifyCode(email, code, 'password_reset');
  if (!verification.success) {
    await VerificationCode.incrementAttempts(email, code, 'password_reset');
    throw new AppError(verification.message, 400);
  }

  // Update password
  user.password = newPassword;
  user.passwordResetToken = undefined;
  user.passwordResetExpires = undefined;
  await user.save();

  res.status(200).json({
    success: true,
    message: 'Password reset successfully',
  });
});

/**
 * Change password (for authenticated users)
 */
export const changePassword = asyncHandler(async (req: Request, res: Response) => {
  const { currentPassword, newPassword } = req.body;
  const userId = req.user._id;

  // Find user with password
  const user = await User.findById(userId).select('+password');
  if (!user) {
    throw new AppError('User not found', 404);
  }

  // Verify current password
  const isCurrentPasswordValid = await user.comparePassword(currentPassword);
  if (!isCurrentPasswordValid) {
    throw new AppError('Current password is incorrect', 400);
  }

  // Update password
  user.password = newPassword;
  await user.save();

  res.status(200).json({
    success: true,
    message: 'Password changed successfully',
  });
});

/**
 * Logout user (client-side token removal)
 */
export const logout = asyncHandler(async (_req: Request, res: Response) => {
  res.status(200).json({
    success: true,
    message: 'Logged out successfully',
  });
});

/**
 * Get current user profile
 */
export const getProfile = asyncHandler(async (req: Request, res: Response) => {
  const user = req.user;

  res.status(200).json({
    success: true,
    message: 'Profile retrieved successfully',
    data: {
      user: {
        id: user._id,
        firstName: user.firstName,
        lastName: user.lastName,
        email: user.email,
        role: user.role,
        isEmailVerified: user.isEmailVerified,
        onboardingComplete: user.onboardingComplete,
        lastLogin: user.lastLogin,
        createdAt: user.createdAt,
        updatedAt: user.updatedAt,
      },
    },
  });
});
