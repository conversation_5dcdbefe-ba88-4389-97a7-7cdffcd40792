import { Document } from 'mongoose';
import { Request } from 'express';

// User Types
export interface IUser extends Document {
  _id: string;
  firstName: string;
  lastName: string;
  email: string;
  password: string;
  role: 'farmer' | 'buyer';
  isEmailVerified: boolean;
  onboardingComplete: boolean;
  emailVerificationToken?: string | undefined;
  emailVerificationExpires?: Date | undefined;
  passwordResetToken?: string | undefined;
  passwordResetExpires?: Date | undefined;
  lastLogin?: Date;
  isActive: boolean;
  createdAt: Date;
  updatedAt: Date;

  // Methods
  comparePassword(candidatePassword: string): Promise<boolean>;
  generateEmailVerificationToken(): string;
  generatePasswordResetToken(): string;
}

// Farmer Details Types
export interface IFarmerDetails extends Document {
  _id: string;
  userId: string;
  landSize: string;
  farmSize: string;
  location: string;
  coordinates?: {
    latitude: number;
    longitude: number;
  };
  cropTypes: string[];
  farmingExperience?: string;
  farmingMethods?: string[];
  certifications?: string[];
  profileImage?: string;
  createdAt: Date;
  updatedAt: Date;
}

// Verification Code Types
export interface IVerificationCode extends Document {
  _id: string;
  email: string;
  code: string;
  type: 'email_verification' | 'password_reset' | 'login_otp';
  expiresAt: Date;
  isUsed: boolean;
  attempts: number;
  maxAttempts: number;
  createdAt: Date;
  updatedAt: Date;
}

// API Response Types
export interface ApiResponse<T = any> {
  success: boolean;
  message: string;
  data?: T | undefined;
  error?: string;
  errors?: Record<string, string> | undefined;
}

// Auth Types
export interface LoginRequest {
  email: string;
  password: string;
}

export interface SignupRequest {
  firstName: string;
  lastName: string;
  email: string;
  password: string;
  role: 'farmer' | 'buyer';
}

export interface FarmerDetailsRequest {
  landSize: string;
  farmSize: string;
  location: string;
  coordinates?: {
    latitude: number;
    longitude: number;
  };
  cropTypes: string[];
  farmingExperience?: string;
  farmingMethods?: string[];
  certifications?: string[];
}

export interface VerifyEmailRequest {
  email: string;
  code: string;
}

export interface ResendVerificationRequest {
  email: string;
}

export interface ForgotPasswordRequest {
  email: string;
}

export interface ResetPasswordRequest {
  email: string;
  code: string;
  newPassword: string;
}

export interface ChangePasswordRequest {
  currentPassword: string;
  newPassword: string;
}

// JWT Payload
export interface JWTPayload {
  userId: string;
  email: string;
  role: string;
  iat?: number;
  exp?: number;
}

// Request with User
export interface AuthenticatedRequest extends Request {
  user?: IUser;
}

// Pagination
export interface PaginationQuery {
  page?: number;
  limit?: number;
  sort?: string;
  order?: 'asc' | 'desc';
}

export interface PaginatedResponse<T> {
  data: T[];
  pagination: {
    page: number;
    limit: number;
    total: number;
    pages: number;
    hasNext: boolean;
    hasPrev: boolean;
  };
}

// Error Types
export class AppError extends Error {
  statusCode: number;
  isOperational: boolean;

  constructor(message: string, statusCode: number) {
    super(message);
    this.statusCode = statusCode;
    this.isOperational = true;

    Error.captureStackTrace(this, this.constructor);
  }
}

// Crop Planning Types
export interface ICrop extends Document {
  _id: string;
  name: string;
  category: string;
  description?: string;
  image?: string;
  growthStages: {
    stage: string;
    duration: number; // in days
    description: string;
  }[];
  requirements: {
    soilType: string[];
    climate: string[];
    waterRequirement: 'low' | 'medium' | 'high';
    sunlightRequirement: 'partial' | 'full';
  };
  benefits: string[];
  marketInfo: {
    averagePrice: number;
    currency: string;
    demandLevel: 'low' | 'medium' | 'high';
    exportPotential: boolean;
  };
  seasonInfo: {
    plantingMonths: number[];
    harvestMonths: number[];
    totalDuration: number; // in days
  };
  suitableFor: {
    landSizes: string[];
    farmingExperience: string[];
    farmingMethods: string[];
  };
  isActive: boolean;
  createdAt: Date;
  updatedAt: Date;
}

export interface ICropPlan extends Document {
  _id: string;
  farmerId: string;
  cropId: string;
  plantingDate: Date;
  expectedHarvestDate: Date;
  actualHarvestDate?: Date;
  landArea: number; // in square meters or hectares
  landUnit: 'sqm' | 'hectare' | 'acre';
  currentStage: string;
  progress: number; // percentage 0-100
  status: 'planned' | 'planted' | 'growing' | 'harvested' | 'failed';
  notes?: string;
  estimatedYield?: number;
  actualYield?: number;
  yieldUnit?: string;
  estimatedProfit?: number;
  actualProfit?: number;
  expenses: {
    seeds: number;
    fertilizer: number;
    pesticides: number;
    labor: number;
    equipment: number;
    other: number;
  };
  revenue?: number;
  createdAt: Date;
  updatedAt: Date;
}

export interface IFarmingActivity extends Document {
  _id: string;
  farmerId: string;
  cropPlanId: string;
  activityType: 'planting' | 'fertilizing' | 'watering' | 'weeding' | 'pest_control' | 'harvesting' | 'other';
  title: string;
  description?: string;
  scheduledDate: Date;
  completedDate?: Date;
  status: 'scheduled' | 'in_progress' | 'completed' | 'skipped' | 'overdue';
  priority: 'low' | 'medium' | 'high';
  reminderSent: boolean;
  notes?: string;
  cost?: number;
  createdAt: Date;
  updatedAt: Date;
}

export interface ICropRecommendation extends Document {
  _id: string;
  farmerId: string;
  cropId: string;
  score: number; // 0-100 recommendation score
  reasons: string[];
  estimatedProfit: number;
  riskLevel: 'low' | 'medium' | 'high';
  seasonality: {
    bestPlantingMonth: number;
    bestHarvestMonth: number;
  };
  requirements: {
    landSizeNeeded: number;
    landUnit: string;
    investmentRequired: number;
  };
  marketOpportunity: {
    demandTrend: 'increasing' | 'stable' | 'decreasing';
    competitionLevel: 'low' | 'medium' | 'high';
    priceStability: 'stable' | 'volatile';
  };
  isActive: boolean;
  validUntil: Date;
  createdAt: Date;
  updatedAt: Date;
}

// Request Types for Crop Planning
export interface CreateCropPlanRequest {
  cropId: string;
  plantingDate: string;
  landArea: number;
  landUnit: 'sqm' | 'hectare' | 'acre';
  notes?: string;
}

export interface UpdateCropPlanRequest {
  currentStage?: string;
  progress?: number;
  status?: 'planned' | 'planted' | 'growing' | 'harvested' | 'failed';
  notes?: string;
  actualHarvestDate?: string;
  actualYield?: number;
  yieldUnit?: string;
  actualProfit?: number;
  expenses?: {
    seeds?: number;
    fertilizer?: number;
    pesticides?: number;
    labor?: number;
    equipment?: number;
    other?: number;
  };
  revenue?: number;
}

export interface CreateFarmingActivityRequest {
  cropPlanId: string;
  activityType: 'planting' | 'fertilizing' | 'watering' | 'weeding' | 'pest_control' | 'harvesting' | 'other';
  title: string;
  description?: string;
  scheduledDate: string;
  priority?: 'low' | 'medium' | 'high';
  cost?: number;
}

export interface UpdateFarmingActivityRequest {
  title?: string;
  description?: string;
  scheduledDate?: string;
  completedDate?: string;
  status?: 'scheduled' | 'in_progress' | 'completed' | 'skipped' | 'overdue';
  priority?: 'low' | 'medium' | 'high';
  notes?: string;
  cost?: number;
}

export interface CropRecommendationQuery {
  landSize?: string;
  location?: string;
  experience?: string;
  preferredCrops?: string[];
  season?: number; // month number
  riskLevel?: 'low' | 'medium' | 'high';
  limit?: number;
}

// Marketplace Types
export interface IProduct extends Document {
  _id: string;
  name: string;
  description?: string;
  price: number;
  originalPrice?: number;
  currency: string;
  images: string[];
  category: string;
  subcategory?: string;
  type: 'input' | 'output'; // input = farming supplies, output = harvested products
  unit?: string; // e.g., "kg", "piece", "liter", "bag"
  stock: number;
  minOrderQuantity: number;
  maxOrderQuantity?: number;
  supplier: {
    id: string;
    name: string;
    type: 'verified' | 'local_farmer' | 'distributor';
    rating?: number;
    location?: string;
  };
  specifications?: {
    brand?: string;
    variety?: string;
    weight?: string;
    dimensions?: string;
    expiryDate?: Date;
    harvestDate?: Date;
    organicCertified?: boolean;
    [key: string]: any;
  };
  rating: {
    average: number;
    count: number;
  };
  discount?: {
    percentage: number;
    validUntil?: Date;
    minQuantity?: number;
  };
  tags: string[];
  isActive: boolean;
  isFeatured: boolean;
  createdAt: Date;
  updatedAt: Date;
}

export interface ICategory extends Document {
  _id: string;
  name: string;
  description?: string;
  icon: string;
  type: 'input' | 'output';
  parentCategory?: string;
  subcategories?: string[];
  productCount: number;
  isActive: boolean;
  sortOrder: number;
  createdAt: Date;
  updatedAt: Date;
}

export interface ICart extends Document {
  _id: string;
  userId: string;
  items: {
    productId: string;
    quantity: number;
    selectedOptions?: {
      [key: string]: string;
    };
    addedAt: Date;
  }[];
  totalAmount: number;
  currency: string;
  expiresAt: Date;
  createdAt: Date;
  updatedAt: Date;
}

export interface IOrder extends Document {
  _id: string;
  orderNumber: string;
  userId: string;
  items: {
    productId: string;
    product: {
      name: string;
      price: number;
      image: string;
      unit?: string;
    };
    quantity: number;
    unitPrice: number;
    totalPrice: number;
    selectedOptions?: {
      [key: string]: string;
    };
  }[];
  totalAmount: number;
  currency: string;
  status: 'pending' | 'confirmed' | 'processing' | 'shipped' | 'delivered' | 'cancelled' | 'refunded';
  paymentStatus: 'pending' | 'paid' | 'failed' | 'refunded';
  paymentMethod?: string;
  paymentReference?: string;
  shippingAddress: {
    fullName: string;
    phone: string;
    address: string;
    city: string;
    state: string;
    postalCode?: string;
    country: string;
  };
  deliveryMethod: 'pickup' | 'delivery';
  estimatedDeliveryDate?: Date;
  actualDeliveryDate?: Date;
  notes?: string;
  trackingNumber?: string;
  createdAt: Date;
  updatedAt: Date;
}

// Request Types for Marketplace
export interface CreateProductRequest {
  name: string;
  description?: string;
  price: number;
  originalPrice?: number;
  currency: string;
  images: string[];
  category: string;
  subcategory?: string;
  type: 'input' | 'output';
  unit?: string;
  stock: number;
  minOrderQuantity: number;
  maxOrderQuantity?: number;
  supplier: {
    id: string;
    name: string;
    type: 'verified' | 'local_farmer' | 'distributor';
    rating?: number;
    location?: string;
  };
  specifications?: {
    [key: string]: any;
  };
  discount?: {
    percentage: number;
    validUntil?: string;
    minQuantity?: number;
  };
  tags: string[];
  isFeatured?: boolean;
}

export interface UpdateProductRequest {
  name?: string;
  description?: string;
  price?: number;
  originalPrice?: number;
  images?: string[];
  category?: string;
  subcategory?: string;
  unit?: string;
  stock?: number;
  minOrderQuantity?: number;
  maxOrderQuantity?: number;
  specifications?: {
    [key: string]: any;
  };
  discount?: {
    percentage: number;
    validUntil?: string;
    minQuantity?: number;
  };
  tags?: string[];
  isFeatured?: boolean;
  isActive?: boolean;
}

export interface AddToCartRequest {
  productId: string;
  quantity: number;
  selectedOptions?: {
    [key: string]: string;
  };
}

export interface UpdateCartItemRequest {
  quantity: number;
  selectedOptions?: {
    [key: string]: string;
  };
}

export interface CreateOrderRequest {
  items: {
    productId: string;
    quantity: number;
    selectedOptions?: {
      [key: string]: string;
    };
  }[];
  shippingAddress: {
    fullName: string;
    phone: string;
    address: string;
    city: string;
    state: string;
    postalCode?: string;
    country: string;
  };
  deliveryMethod: 'pickup' | 'delivery';
  paymentMethod?: string;
  notes?: string;
}

export interface ProductSearchQuery {
  q?: string; // search query
  category?: string;
  type?: 'input' | 'output';
  minPrice?: number;
  maxPrice?: number;
  supplier?: string;
  tags?: string[];
  featured?: boolean;
  inStock?: boolean;
  page?: number;
  limit?: number;
  sort?: 'name' | 'price' | 'rating' | 'newest' | 'featured';
  order?: 'asc' | 'desc';
}

// Email Template Types
export interface EmailTemplate {
  subject: string;
  html: string;
  text?: string;
}

export interface EmailContext {
  firstName: string;
  email: string;
  code?: string;
  resetUrl?: string;
  verificationUrl?: string;
  [key: string]: any;
}
